{"name": "portfolio", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@heroicons/react": "^2.2.0", "@tailwindcss/forms": "^0.5.10", "classnames": "^2.5.1", "framer-motion": "^12.15.0", "gsap": "^3.13.0", "lucide-react": "^0.279.0", "next": "^14.0.0", "next-themes": "^0.4.6", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^5.5.0", "react-intersection-observer": "^9.16.0"}, "devDependencies": {"@types/node": "^20.8.0", "@types/react": "^18.2.24", "@types/react-dom": "^18.2.8", "autoprefixer": "^10.4.14", "eslint": "^8.50.0", "eslint-config-next": "^14.0.0", "postcss": "^8.4.27", "tailwindcss": "^3.3.3", "typescript": "^5.2.2"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}