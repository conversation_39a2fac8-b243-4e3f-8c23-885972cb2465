'use client';

import { useState } from 'react';
import { FileDown, Download, ExternalLink, Clock, Check, Eye } from 'lucide-react';
import Link from 'next/link';
import { motion, AnimatePresence } from 'framer-motion';

export default function CvDownload() {
  const [isDownloading, setIsDownloading] = useState(false);
  const [isDownloaded, setIsDownloaded] = useState(false);
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  
  const handleDownload = () => {
    setIsDownloading(true);
    // Simulate download delay
    setTimeout(() => {
      setIsDownloading(false);
      setIsDownloaded(true);
      setTimeout(() => setIsDownloaded(false), 3000);
    }, 1500);
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-xl overflow-hidden">
      <div className="relative overflow-hidden">
        {/* CV Preview Image */}
        <div className="h-64 bg-gradient-to-r from-indigo-500 to-purple-600 relative">
          <div className="absolute inset-0 flex items-center justify-center">
            <motion.div 
              className="bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm p-6 rounded-lg shadow-lg w-48 h-64 transform rotate-6"
              whileHover={{ rotate: 0 }}
              transition={{ type: "spring", stiffness: 300, damping: 20 }}
            >
              <div className="h-6 bg-indigo-600 rounded-sm mb-3"></div>
              <div className="space-y-2">
                <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded-sm w-full"></div>
                <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded-sm w-5/6"></div>
                <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded-sm w-4/6"></div>
              </div>
              <div className="mt-4 space-y-2">
                <div className="h-2 bg-gray-200 dark:bg-gray-700 rounded-sm w-full"></div>
                <div className="h-2 bg-gray-200 dark:bg-gray-700 rounded-sm w-full"></div>
                <div className="h-2 bg-gray-200 dark:bg-gray-700 rounded-sm w-5/6"></div>
                <div className="h-2 bg-gray-200 dark:bg-gray-700 rounded-sm w-full"></div>
              </div>
              <div className="mt-4 flex justify-between">
                <div className="h-8 w-8 bg-indigo-100 dark:bg-indigo-900/30 rounded-full"></div>
                <div className="h-8 w-8 bg-indigo-100 dark:bg-indigo-900/30 rounded-full"></div>
                <div className="h-8 w-8 bg-indigo-100 dark:bg-indigo-900/30 rounded-full"></div>
              </div>
            </motion.div>
          </div>
        </div>
        
        {/* CV Preview Modal */}
        <AnimatePresence>
          {isPreviewOpen && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm"
              onClick={() => setIsPreviewOpen(false)}
            >
              <motion.div
                initial={{ scale: 0.9, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                exit={{ scale: 0.9, opacity: 0 }}
                className="bg-white dark:bg-gray-800 p-4 rounded-xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-auto"
                onClick={(e) => e.stopPropagation()}
              >
                <div className="flex justify-between items-center mb-4">
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white">Resume Preview</h3>
                  <button 
                    onClick={() => setIsPreviewOpen(false)}
                    className="p-1 rounded-full bg-gray-100 dark:bg-gray-700 text-gray-500 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-600"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><line x1="18" y1="6" x2="6" y2="18"></line><line x1="6" y1="6" x2="18" y2="18"></line></svg>
                  </button>
                </div>
                
                <div className="aspect-[8.5/11] bg-white dark:bg-gray-900 rounded shadow-md p-8">
                  {/* Resume Content Preview */}
                  <div className="border-b border-gray-200 dark:border-gray-700 pb-4 mb-4">
                    <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Magdi Mohamed</h1>
                    <p className="text-indigo-600 dark:text-indigo-400">Product Manager & Supply Chain Specialist</p>
                    <div className="flex flex-wrap gap-3 mt-2 text-sm text-gray-600 dark:text-gray-400">
                      <span><EMAIL></span>
                      <span>+201501056664</span>
                      <span>Cairo, Egypt</span>
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <div>
                      <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Professional Summary</h2>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                        Results-driven professional with extensive experience in supply chain management,
                        product development, and team leadership. Proven track record in implementing
                        technological solutions that drive business growth and operational efficiency.
                      </p>
                    </div>
                    
                    <div>
                      <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Experience</h2>
                      <div className="space-y-2 mt-1">
                        <div>
                          <div className="flex justify-between">
                            <h3 className="text-sm font-medium">Product Manager, Zawaya</h3>
                            <span className="text-xs text-gray-500 dark:text-gray-400">2024</span>
                          </div>
                          <p className="text-xs text-gray-600 dark:text-gray-400">Led product development initiatives, focusing on e-commerce solutions.</p>
                        </div>
                        <div>
                          <div className="flex justify-between">
                            <h3 className="text-sm font-medium">Supply Chain Manager, Zawaya</h3>
                            <span className="text-xs text-gray-500 dark:text-gray-400">2022-2024</span>
                          </div>
                          <p className="text-xs text-gray-600 dark:text-gray-400">Managed end-to-end supply chain operations.</p>
                        </div>
                      </div>
                    </div>
                    
                    {/* Preview shows truncated content with ellipsis */}
                    <div className="text-center mt-8">
                      <div className="inline-block px-4 py-2 border border-dashed border-gray-300 dark:border-gray-600 rounded-md text-gray-500 dark:text-gray-400 text-sm">
                        Preview shows limited content. Download the full resume for complete details.
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="mt-4 flex justify-end">
                  <Link 
                    href="/assets/magdi_mohamed_cv.pdf" 
                    target="_blank"
                    onClick={handleDownload}
                    className="inline-flex items-center px-4 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg transition-all"
                  >
                    <Download className="w-4 h-4 mr-2" />
                    Download Full Resume
                  </Link>
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
      
      <div className="p-6">
        <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-6">
          <div>
            <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">Professional Resume</h3>
            <p className="text-gray-600 dark:text-gray-400">
              Download my comprehensive resume highlighting my experience, skills, and qualifications. 
              Perfect for recruiters and potential collaborators.
            </p>
            
            <div className="mt-4 flex flex-wrap gap-3">
              <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                <Clock className="w-4 h-4 mr-1" />
                <span>Last updated: June 2024</span>
              </div>
              <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-1"><path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"></path><polyline points="13 2 13 9 20 9"></polyline></svg>
                <span>PDF Format (2 pages)</span>
              </div>
            </div>
          </div>
          
          <div className="flex flex-col sm:flex-row gap-3">
            <motion.button
              whileHover={{ scale: 1.03 }}
              whileTap={{ scale: 0.97 }}
              onClick={() => setIsPreviewOpen(true)}
              className="inline-flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            >
              <Eye className="w-4 h-4 mr-2" />
              Preview Resume
            </motion.button>
            
            <Link 
              href="/assets/magdi_mohamed_cv.pdf" 
              target="_blank"
              onClick={handleDownload}
              className="relative overflow-hidden"
            >
              <motion.div
                className={`inline-flex items-center justify-center px-4 py-2 rounded-lg transition-all ${
                  isDownloaded 
                    ? 'bg-green-600 text-white' 
                    : 'bg-indigo-600 hover:bg-indigo-700 text-white'
                }`}
                whileHover={{ scale: 1.03 }}
                whileTap={{ scale: 0.97 }}
              >
                {isDownloading ? (
                  <>
                    <div className="animate-spin h-4 w-4 mr-2 border-2 border-t-transparent border-white rounded-full"></div>
                    Downloading...
                  </>
                ) : isDownloaded ? (
                  <>
                    <Check className="w-4 h-4 mr-2" />
                    Downloaded
                  </>
                ) : (
                  <>
                    <Download className="w-4 h-4 mr-2" />
                    Download Resume
                  </>
                )}
              </motion.div>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
} 