'use client';

import { useState } from 'react';
import { ChevronLeft, ChevronRight, Star } from 'lucide-react';

interface Testimonial {
  id: number;
  name: string;
  role: string;
  company: string;
  content: string;
  rating: number;
  image?: string;
}

const testimonials: Testimonial[] = [
  {
    id: 1,
    name: '<PERSON>',
    role: 'CTO',
    company: '<PERSON>awaya',
    content: '<PERSON><PERSON><PERSON> transformed our internal systems with his exceptional product management skills. His ability to understand complex business needs and translate them into effective technological solutions has been invaluable to our company growth.',
    rating: 5,
  },
  {
    id: 2,
    name: '<PERSON>',
    role: 'Operations Director',
    company: 'Capiter',
    content: 'Working with <PERSON><PERSON><PERSON> was a game-changer for our supply chain operations. His analytical approach and dedication to process improvement helped us reduce costs and increase efficiency significantly.',
    rating: 5,
  },
  {
    id: 3,
    name: '<PERSON>',
    role: 'Head of Technology',
    company: 'SWVL',
    content: 'Mag<PERSON>\'s ability to bridge the gap between technical teams and business objectives is remarkable. He consistently delivered projects that not only met but exceeded our expectations.',
    rating: 4,
  },
];

export default function Testimonials() {
  const [currentIndex, setCurrentIndex] = useState(0);

  const nextTestimonial = () => {
    setCurrentIndex((prevIndex) => (prevIndex + 1) % testimonials.length);
  };

  const prevTestimonial = () => {
    setCurrentIndex((prevIndex) => (prevIndex - 1 + testimonials.length) % testimonials.length);
  };

  return (
    <div className="relative overflow-hidden bg-white dark:bg-gray-800 rounded-lg shadow-md">
      <div className="px-6 py-8">
        <div className="text-center mb-8">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Client Testimonials</h2>
          <p className="mt-2 text-gray-600 dark:text-gray-400">What people say about working with me</p>
        </div>

        <div className="flex items-center justify-center">
          <button
            onClick={prevTestimonial}
            className="p-2 mr-2 rounded-full bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 hover:bg-indigo-100 dark:hover:bg-indigo-900 transition-colors"
            aria-label="Previous testimonial"
          >
            <ChevronLeft className="w-5 h-5" />
          </button>

          <div className="max-w-2xl mx-auto">
            <div className="flex flex-col items-center text-center p-6 bg-gray-50 dark:bg-gray-900 rounded-lg">
              <div className="flex mb-4">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className={`w-5 h-5 ${
                      i < testimonials[currentIndex].rating
                        ? 'text-yellow-500 fill-yellow-500'
                        : 'text-gray-300 dark:text-gray-600'
                    }`}
                  />
                ))}
              </div>
              <p className="text-gray-700 dark:text-gray-300 mb-6 italic">"{testimonials[currentIndex].content}"</p>
              <div>
                <p className="font-semibold text-gray-900 dark:text-white">{testimonials[currentIndex].name}</p>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {testimonials[currentIndex].role}, {testimonials[currentIndex].company}
                </p>
              </div>
            </div>
          </div>

          <button
            onClick={nextTestimonial}
            className="p-2 ml-2 rounded-full bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 hover:bg-indigo-100 dark:hover:bg-indigo-900 transition-colors"
            aria-label="Next testimonial"
          >
            <ChevronRight className="w-5 h-5" />
          </button>
        </div>

        <div className="flex justify-center mt-4">
          {testimonials.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentIndex(index)}
              className={`w-2 h-2 mx-1 rounded-full ${
                currentIndex === index ? 'bg-indigo-600' : 'bg-gray-300 dark:bg-gray-600'
              }`}
              aria-label={`Go to testimonial ${index + 1}`}
            />
          ))}
        </div>
      </div>
    </div>
  );
} 