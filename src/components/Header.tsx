'use client';

import { useState, useEffect } from 'react';
import { ThemeToggle } from './theme/theme-toggle';
import Link from 'next/link';
import { motion, AnimatePresence } from 'framer-motion';
import { Terminal, Code, Zap } from 'lucide-react';

const navigationItems = [
  { name: 'ABOUT', href: '#about', icon: '01' },
  { name: 'PROJECTS', href: '#projects', icon: '02' },
  { name: 'CONTACT', href: '#contact', icon: '03' },
];

export default function Header() {
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const [activeSection, setActiveSection] = useState('');

  useEffect(() => {
    const handleScroll = () => {
      const offset = window.scrollY;
      setScrolled(offset > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const headerVariants = {
    initial: {
      y: -100,
      opacity: 0,
    },
    animate: {
      y: 0,
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 260,
        damping: 20,
      },
    },
  };
  
  const logoVariants = {
    initial: { scale: 0.8, opacity: 0 },
    animate: { 
      scale: 1, 
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 260,
        damping: 20,
        delay: 0.2,
      }
    },
    hover: {
      scale: 1.05,
      transition: {
        type: 'spring',
        stiffness: 400,
        damping: 10,
      },
    },
  };
  
  const navItemVariants = {
    closed: { opacity: 0, y: -5 },
    open: (i: number) => ({
      opacity: 1,
      y: 0,
      transition: {
        delay: i * 0.1,
        type: 'spring',
        stiffness: 300,
        damping: 24,
      },
    }),
  };
  
  const mobileMenuVariants = {
    closed: {
      height: 0,
      opacity: 0,
      transition: {
        duration: 0.3,
        ease: "easeInOut",
        when: "afterChildren",
        staggerChildren: 0.05,
        staggerDirection: -1,
      },
    },
    open: {
      height: "auto",
      opacity: 1,
      transition: {
        duration: 0.3,
        ease: "easeInOut",
        when: "beforeChildren",
        staggerChildren: 0.05,
        delayChildren: 0.1,
      },
    },
  };

  return (
    <motion.header
      variants={headerVariants}
      initial="initial"
      animate="animate"
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-500 ${
        scrolled
          ? 'bg-background/95 backdrop-blur-xl border-b border-primary/30 shadow-2xl'
          : 'bg-transparent'
      }`}
    >
      <div className="container-wide">
        <div className="flex h-16 sm:h-20 items-center justify-between">
          {/* Logo */}
          <div className="flex items-center">
            <Link href="/">
              <motion.div
                variants={logoVariants}
                initial="initial"
                animate="animate"
                whileHover="hover"
                className="flex items-center gap-2 sm:gap-3"
              >
                <div className="relative">
                  <Terminal className="w-6 h-6 sm:w-8 sm:h-8 text-primary" />
                  <motion.div
                    className="absolute inset-0 bg-primary/20 rounded-full blur-lg"
                    animate={{
                      scale: [1, 1.2, 1],
                      opacity: [0.5, 0.8, 0.5],
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      ease: "easeInOut",
                    }}
                  />
                </div>
                <div className="flex flex-col">
                  <span className="text-lg sm:text-xl font-bold font-mono text-primary glitch-text" data-text="MAGDI.DEV">
                    MAGDI.DEV
                  </span>
                  <span className="text-xs font-mono text-muted-foreground tracking-wider hidden sm:block">
                    &gt; PRODUCT_MANAGER
                  </span>
                </div>
              </motion.div>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center gap-1">
            {navigationItems.map((item, i) => (
              <motion.div
                key={item.name}
                custom={i}
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{
                  delay: i * 0.1,
                  type: 'spring',
                  stiffness: 300,
                  damping: 24
                }}
              >
                <Link
                  href={item.href}
                  className="relative group px-4 py-2 font-mono text-sm font-medium transition-all duration-300"
                  onClick={() => setActiveSection(item.href)}
                >
                  <span className="relative z-10 flex items-center gap-2">
                    <span className="text-primary/60 text-xs">{item.icon}</span>
                    <span className="text-foreground/80 group-hover:text-primary transition-colors">
                      {item.name}
                    </span>
                  </span>

                  {/* Hover effect */}
                  <motion.div
                    className="absolute inset-0 bg-primary/10 border border-primary/30 rounded-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                    layoutId="navHover"
                  />

                  {/* Active indicator */}
                  {activeSection === item.href && (
                    <motion.div
                      className="absolute bottom-0 left-0 right-0 h-0.5 bg-primary"
                      layoutId="activeNav"
                      initial={false}
                    />
                  )}
                </Link>
              </motion.div>
            ))}

            {/* Theme Toggle */}
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: navigationItems.length * 0.1 }}
              className="ml-4"
            >
              <ThemeToggle />
            </motion.div>
          </nav>

          {/* Mobile Menu Button */}
          <div className="flex items-center lg:hidden">
            <ThemeToggle />
            <motion.button
              type="button"
              className="ml-3 cyber-btn-outline"
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              whileTap={{ scale: 0.95 }}
            >
              <span className="sr-only">Toggle menu</span>
              <motion.div
                animate={mobileMenuOpen ? "open" : "closed"}
                className="w-6 h-6 flex flex-col justify-center items-center"
              >
                <motion.span
                  className="w-5 h-0.5 bg-primary block"
                  variants={{
                    closed: { rotate: 0, y: 0 },
                    open: { rotate: 45, y: 2 }
                  }}
                  transition={{ duration: 0.3 }}
                />
                <motion.span
                  className="w-5 h-0.5 bg-primary block mt-1"
                  variants={{
                    closed: { opacity: 1 },
                    open: { opacity: 0 }
                  }}
                  transition={{ duration: 0.3 }}
                />
                <motion.span
                  className="w-5 h-0.5 bg-primary block mt-1"
                  variants={{
                    closed: { rotate: 0, y: 0 },
                    open: { rotate: -45, y: -2 }
                  }}
                  transition={{ duration: 0.3 }}
                />
              </motion.div>
            </motion.button>
          </div>
        </div>
      </div>

      {/* Mobile Menu */}
      <AnimatePresence>
        {mobileMenuOpen && (
          <motion.div
            className="lg:hidden terminal-window mx-4 mt-4 mb-4"
            variants={mobileMenuVariants}
            initial="closed"
            animate="open"
            exit="closed"
          >
            <div className="terminal-header">
              <div className="terminal-dot red"></div>
              <div className="terminal-dot yellow"></div>
              <div className="terminal-dot green"></div>
              <span className="ml-4 font-mono text-xs text-muted-foreground">navigation.menu</span>
            </div>
            <div className="p-6 space-y-4">
              {navigationItems.map((item, i) => (
                <motion.div
                  key={item.name}
                  custom={i}
                  variants={navItemVariants}
                >
                  <Link
                    href={item.href}
                    className="flex items-center gap-3 p-3 rounded-lg font-mono text-sm font-medium transition-all duration-300 hover:bg-primary/10 hover:text-primary border border-transparent hover:border-primary/30"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    <span className="text-primary/60 text-xs">{item.icon}</span>
                    <span>{item.name}</span>
                    <Code className="w-4 h-4 ml-auto opacity-50" />
                  </Link>
                </motion.div>
              ))}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.header>
  );
}