'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { ChevronDown, ChevronUp, ArrowRight, CheckCircle, AlertTriangle, LightbulbIcon, BarChart } from 'lucide-react';

export interface CaseStudyProps {
  id: string;
  title: string;
  company: string;
  period: string;
  problem: string;
  approach: string[];
  solution: string;
  technologies: string[];
  outcomes: { metric: string; value: string }[];
  image?: string;
}

export default function CaseStudy({ caseStudy }: { caseStudy: CaseStudyProps }) {
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden mb-6">
      {/* Header - Always visible */}
      <div
        className="p-6 cursor-pointer"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex justify-between items-center">
          <div>
            <h3 className="text-xl font-bold text-gray-900 dark:text-white">{caseStudy.title}</h3>
            <p className="text-indigo-600 dark:text-indigo-400">{caseStudy.company} • {caseStudy.period}</p>
          </div>
          <button
            className="p-2 rounded-full bg-gray-100 dark:bg-gray-700 text-gray-500 dark:text-gray-400 hover:bg-indigo-100 dark:hover:bg-indigo-900"
            aria-label={isExpanded ? "Collapse case study" : "Expand case study"}
          >
            {isExpanded ? <ChevronUp /> : <ChevronDown />}
          </button>
        </div>
        
        {/* Problem Statement - Always visible */}
        <div className="mt-4">
          <div className="flex items-start">
            <div className="flex-shrink-0 mr-3">
              <AlertTriangle className="h-6 w-6 text-orange-500" />
            </div>
            <div>
              <h4 className="font-medium text-gray-900 dark:text-white mb-1">The Challenge</h4>
              <p className="text-gray-600 dark:text-gray-300">{caseStudy.problem}</p>
            </div>
          </div>
        </div>
      </div>
      
      {/* Expanded Content */}
      {isExpanded && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: "auto" }}
          exit={{ opacity: 0, height: 0 }}
          transition={{ duration: 0.3 }}
          className="px-6 pb-6"
        >
          {/* Approach */}
          <div className="mt-6">
            <div className="flex items-start">
              <div className="flex-shrink-0 mr-3">
                <LightbulbIcon className="h-6 w-6 text-yellow-500" />
              </div>
              <div>
                <h4 className="font-medium text-gray-900 dark:text-white mb-2">Approach</h4>
                <ul className="space-y-2">
                  {caseStudy.approach.map((step, index) => (
                    <li key={index} className="flex items-start">
                      <ArrowRight className="h-5 w-5 text-indigo-500 mr-2 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-600 dark:text-gray-300">{step}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </div>
          
          {/* Solution */}
          <div className="mt-6">
            <div className="flex items-start">
              <div className="flex-shrink-0 mr-3">
                <CheckCircle className="h-6 w-6 text-green-500" />
              </div>
              <div>
                <h4 className="font-medium text-gray-900 dark:text-white mb-1">Solution</h4>
                <p className="text-gray-600 dark:text-gray-300">{caseStudy.solution}</p>
                
                {/* Technologies */}
                {caseStudy.technologies.length > 0 && (
                  <div className="mt-3">
                    <h5 className="text-sm font-medium text-gray-700 dark:text-gray-400 mb-2">Technologies & Tools</h5>
                    <div className="flex flex-wrap gap-2">
                      {caseStudy.technologies.map((tech, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-300"
                        >
                          {tech}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
          
          {/* Outcomes */}
          <div className="mt-6">
            <div className="flex items-start">
              <div className="flex-shrink-0 mr-3">
                <BarChart className="h-6 w-6 text-blue-500" />
              </div>
              <div>
                <h4 className="font-medium text-gray-900 dark:text-white mb-3">Measurable Outcomes</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {caseStudy.outcomes.map((outcome, index) => (
                    <div key={index} className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                      <p className="text-2xl font-bold text-indigo-600 dark:text-indigo-400">{outcome.value}</p>
                      <p className="text-sm text-gray-600 dark:text-gray-300">{outcome.metric}</p>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
          
          {/* Optional Image */}
          {caseStudy.image && (
            <div className="mt-6">
              <img 
                src={caseStudy.image} 
                alt={`${caseStudy.title} visual`} 
                className="w-full h-auto rounded-lg shadow-sm"
              />
            </div>
          )}
        </motion.div>
      )}
    </div>
  );
} 