'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, <PERSON>R<PERSON>, CheckCircle, AlertTriangle, LightbulbIcon, BarChart, Maximize2 } from 'lucide-react';
import { GlowCard, TiltCard } from '../animations/AnimatedCard';
import { MotionWrapper, MotionFade } from '../animations/MotionWrapper';

export interface CaseStudyProps {
  id: string;
  title: string;
  company: string;
  period: string;
  problem: string;
  approach: string[];
  solution: string;
  technologies: string[];
  outcomes: { metric: string; value: string }[];
  image?: string;
}

const caseStudies: CaseStudyProps[] = [
  {
    id: 'payment-gateway',
    title: 'Online Payment Gateway Integration',
    company: 'Zawaya',
    period: 'January 2024 - April 2024',
    problem: 'Customers were abandoning checkouts due to limited payment options and a complicated payment process, resulting in a 35% cart abandonment rate.',
    approach: [
      'Conducted market research to identify the most popular payment methods in our target regions',
      'Analyzed the checkout flow to identify pain points and opportunities for streamlining',
      'Evaluated multiple payment gateway providers based on fees, features, security, and integration complexity',
      'Created a detailed integration plan with milestones and risk mitigation strategies',
      'Developed a phased rollout strategy to minimize disruption to existing customers'
    ],
    solution: 'Led a cross-functional team to implement a comprehensive payment gateway solution that supported multiple payment methods (credit cards, digital wallets, and local payment options). We redesigned the checkout flow to reduce steps and implemented robust security measures to protect customer data.',
    technologies: ['Shopify Payments API', 'PayPal Integration', 'Stripe', 'Payment Card Industry Data Security Standard (PCI DSS)', 'UI/UX Design'],
    outcomes: [
      { metric: 'Increase in checkout completion', value: '+25%' },
      { metric: 'New payment methods added', value: '6' },
      { metric: 'Reduction in processing time', value: '40%' },
      { metric: 'Increase in international sales', value: '+18%' },
      { metric: 'ROI on implementation', value: '285%' }
    ],
    image: '/assets/images/payment-gateway.jpg'
  },
  {
    id: 'erp-integration',
    title: 'ERP Features Implementation',
    company: 'Zawaya',
    period: 'March 2024 - June 2024',
    problem: 'The company was spending $100K annually on third-party ERP systems that weren\'t fully integrated with our core systems, causing data silos, manual work, and inefficiencies.',
    approach: [
      'Conducted a comprehensive audit of current ERP usage and pain points',
      'Mapped essential ERP features needed for business operations',
      'Developed a cost-benefit analysis for building in-house solutions vs. continuing with third-party systems',
      'Created a phased implementation plan prioritizing high-impact features',
      'Assembled a dedicated development team with ERP expertise'
    ],
    solution: 'Spearheaded the development and integration of key ERP features directly into our core system, including accounting, payment processing, and operations functionalities. We ensured seamless data flow between modules and provided comprehensive training for all users.',
    technologies: ['Cloud Architecture', 'API Development', 'Database Design', 'Workflow Automation', 'Data Migration'],
    outcomes: [
      { metric: 'Annual cost savings', value: '$100K' },
      { metric: 'Reduction in manual data entry', value: '75%' },
      { metric: 'Improvement in reporting speed', value: '60%' },
      { metric: 'Process automation increase', value: '85%' },
      { metric: 'User adoption rate', value: '92%' }
    ],
    image: '/assets/images/erp-system.jpg'
  },
  {
    id: 'delivery-app',
    title: 'Internal Flutter Delivery Management App',
    company: 'Zawaya',
    period: 'November 2023 - Present',
    problem: 'Lack of real-time visibility into delivery operations was causing inefficient routing, poor resource allocation, and customer dissatisfaction with delivery times.',
    approach: [
      'Shadowed delivery agents to understand real-world challenges and needs',
      'Conducted interviews with operations managers to identify key performance indicators',
      'Researched best practices in delivery management technology',
      'Created detailed wireframes and user stories for the application',
      'Implemented agile development methodology with two-week sprints'
    ],
    solution: 'Designed, developed, and deployed a Flutter-based delivery management application that provides real-time tracking of agents\' performance, availability, and location. The app includes route optimization, delivery confirmation, and performance analytics dashboards for management.',
    technologies: ['Flutter', 'Dart', 'Firebase', 'Google Maps API', 'Real-time Database', 'Push Notifications'],
    outcomes: [
      { metric: 'Reduction in delivery times', value: '28%' },
      { metric: 'Increase in deliveries per agent', value: '+35%' },
      { metric: 'Fuel cost savings', value: '22%' },
      { metric: 'Improvement in on-time delivery', value: '94%' },
      { metric: 'Reduction in complaints', value: '45%' }
    ],
    image: '/assets/images/delivery-app.jpg'
  },
  {
    id: 'crm-webapp',
    title: 'E-commerce CRM Web Application',
    company: 'Zawaya',
    period: 'July 2023 - December 2023',
    problem: 'Fragmented customer data across multiple platforms was preventing personalized marketing and causing inefficient customer service responses.',
    approach: [
      'Analyzed existing customer data structures and touchpoints',
      'Defined key performance indicators for customer relationship management',
      'Designed a unified customer data model',
      'Developed API integrations with existing systems',
      'Created a phased implementation plan with minimal disruption'
    ],
    solution: 'Developed a centralized CRM web application that consolidates customer data from all touchpoints, provides a 360-degree customer view, and enables personalized marketing automation and efficient customer service.',
    technologies: ['React', 'Node.js', 'GraphQL', 'MongoDB', 'AWS', 'Marketing Automation'],
    outcomes: [
      { metric: 'Increase in customer retention', value: '28%' },
      { metric: 'Improvement in response time', value: '65%' },
      { metric: 'Growth in repeat purchases', value: '42%' },
      { metric: 'ROI within first year', value: '310%' },
      { metric: 'Reduction in data silos', value: '95%' }
    ],
    image: '/assets/images/crm-webapp.jpg'
  }
];

export default function CaseStudiesList() {
  const [selectedCase, setSelectedCase] = useState<CaseStudyProps | null>(null);
  const [activeIndex, setActiveIndex] = useState(0);

  return (
    <MotionWrapper>
      <div className="space-y-8">
        <MotionFade>
          <p className="text-gray-600 dark:text-gray-400 mb-8 text-center max-w-3xl mx-auto text-lg">
            Explore detailed case studies showcasing my approach to solving complex business challenges
            through innovative solutions and measurable results.
          </p>
        </MotionFade>

        {/* Horizontal Scrolling Case Studies */}
        <div className="relative">
          <div className="overflow-x-auto scrollbar-hide">
            <div className="flex gap-8 pb-6" style={{ minWidth: 'max-content' }}>
              {caseStudies.map((caseStudy, index) => (
                <motion.div
                  key={caseStudy.id}
                  initial={{ opacity: 0, x: 50 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="flex-shrink-0 w-96"
                >
                  <GlowCard
                    delay={index * 0.1}
                    onClick={() => setSelectedCase(caseStudy)}
                    className="bg-white dark:bg-gray-800 shadow-xl h-full cursor-pointer hover:shadow-2xl transition-all duration-300 border border-gray-200 dark:border-gray-700 rounded-2xl overflow-hidden"
                  >
                    {caseStudy.image && (
                <div className="relative h-48 overflow-hidden">
                  <img 
                    src={caseStudy.image} 
                    alt={caseStudy.title} 
                    className="w-full h-full object-cover object-center transform transition-transform duration-700 hover:scale-110"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent"></div>
                  <div className="absolute bottom-0 left-0 p-4 text-white">
                    <p className="text-sm font-medium">{caseStudy.company}</p>
                    <p className="text-xs opacity-80">{caseStudy.period}</p>
                  </div>
                  <motion.div 
                    className="absolute top-3 right-3 p-1.5 bg-white/20 backdrop-blur-sm rounded-full opacity-70 hover:opacity-100"
                    whileHover={{ scale: 1.1 }}
                  >
                    <Maximize2 className="h-4 w-4 text-white" />
                  </motion.div>
                </div>
                    )}
                    <div className="p-6">
                <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">{caseStudy.title}</h3>
                
                <div className="flex items-start mb-4">
                  <AlertTriangle className="h-5 w-5 text-orange-500 mr-2 flex-shrink-0 mt-0.5" />
                  <p className="text-sm text-gray-600 dark:text-gray-300 line-clamp-3">{caseStudy.problem}</p>
                </div>
                
                <div className="flex flex-wrap gap-2 mb-4">
                  {caseStudy.technologies.slice(0, 3).map((tech, i) => (
                    <span key={i} className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-300">
                      {tech}
                    </span>
                  ))}
                  {caseStudy.technologies.length > 3 && (
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300">
                      +{caseStudy.technologies.length - 3} more
                    </span>
                  )}
                </div>
                
                <div className="grid grid-cols-2 gap-2">
                  {caseStudy.outcomes.slice(0, 2).map((outcome, i) => (
                    <div key={i} className="bg-gray-50 dark:bg-gray-700/50 p-3 rounded-lg">
                      <p className="text-lg font-bold text-indigo-600 dark:text-indigo-400">{outcome.value}</p>
                      <p className="text-xs text-gray-600 dark:text-gray-300 truncate">{outcome.metric}</p>
                    </div>
                  ))}
                </div>
                    </div>
                  </GlowCard>
                </motion.div>
              ))}
            </div>
          </div>
        </div>

        {/* Case Study Detail Modal */}
        <AnimatePresence>
          {selectedCase && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm"
              onClick={() => setSelectedCase(null)}
            >
              <motion.div
                initial={{ opacity: 0, y: 20, scale: 0.95 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                exit={{ opacity: 0, y: 20, scale: 0.95 }}
                transition={{ type: 'spring', damping: 25 }}
                className="bg-white dark:bg-gray-800 w-full max-w-4xl max-h-[90vh] overflow-y-auto rounded-xl shadow-2xl"
                onClick={(e) => e.stopPropagation()}
              >
                <div className="relative">
                  {selectedCase.image && (
                    <div className="h-64 sm:h-80 overflow-hidden">
                      <img 
                        src={selectedCase.image} 
                        alt={selectedCase.title} 
                        className="w-full h-full object-cover"
                      />
                      <div className="absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-black/80"></div>
                    </div>
                  )}
                  
                  <button 
                    className="absolute top-4 right-4 p-2 bg-black/20 hover:bg-black/40 rounded-full text-white transition-all"
                    onClick={() => setSelectedCase(null)}
                  >
                    <X className="h-5 w-5" />
                  </button>
                  
                  <div className={`absolute bottom-0 left-0 w-full p-6 text-white ${!selectedCase.image ? 'relative bg-gradient-to-r from-indigo-600 to-purple-600 rounded-t-xl' : ''}`}>
                    <h2 className="text-2xl sm:text-3xl font-bold">{selectedCase.title}</h2>
                    <p className="text-white/80">{selectedCase.company} • {selectedCase.period}</p>
                  </div>
                </div>
                
                <div className="p-6 sm:p-8">
                  <div className="space-y-8">
                    {/* The Challenge */}
                    <div className="flex">
                      <div className="flex-shrink-0 mr-4">
                        <div className="w-12 h-12 rounded-full bg-orange-100 dark:bg-orange-900/30 flex items-center justify-center">
                          <AlertTriangle className="h-6 w-6 text-orange-500" />
                        </div>
                      </div>
                      <div>
                        <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">The Challenge</h3>
                        <p className="text-gray-600 dark:text-gray-300">{selectedCase.problem}</p>
                      </div>
                    </div>
                    
                    {/* Approach */}
                    <div className="flex">
                      <div className="flex-shrink-0 mr-4">
                        <div className="w-12 h-12 rounded-full bg-yellow-100 dark:bg-yellow-900/30 flex items-center justify-center">
                          <LightbulbIcon className="h-6 w-6 text-yellow-500" />
                        </div>
                      </div>
                      <div>
                        <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">My Approach</h3>
                        <ul className="space-y-2">
                          {selectedCase.approach.map((step, index) => (
                            <motion.li 
                              key={index} 
                              className="flex items-start"
                              initial={{ opacity: 0, x: -10 }}
                              animate={{ opacity: 1, x: 0 }}
                              transition={{ delay: index * 0.1 }}
                            >
                              <ArrowRight className="h-5 w-5 text-indigo-500 mr-2 mt-0.5 flex-shrink-0" />
                              <span className="text-gray-600 dark:text-gray-300">{step}</span>
                            </motion.li>
                          ))}
                        </ul>
                      </div>
                    </div>
                    
                    {/* Solution */}
                    <div className="flex">
                      <div className="flex-shrink-0 mr-4">
                        <div className="w-12 h-12 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center">
                          <CheckCircle className="h-6 w-6 text-green-500" />
                        </div>
                      </div>
                      <div>
                        <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-3">The Solution</h3>
                        <p className="text-gray-600 dark:text-gray-300 mb-4">{selectedCase.solution}</p>
                        
                        {/* Technologies */}
                        <div className="mb-2">
                          <h4 className="text-sm font-semibold uppercase tracking-wide text-gray-500 dark:text-gray-400 mb-3">Technologies & Tools</h4>
                          <div className="flex flex-wrap gap-2">
                            {selectedCase.technologies.map((tech, index) => (
                              <motion.span
                                key={index}
                                initial={{ opacity: 0, scale: 0.8 }}
                                animate={{ opacity: 1, scale: 1 }}
                                transition={{ delay: index * 0.05 }}
                                className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-300"
                              >
                                {tech}
                              </motion.span>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    {/* Outcomes */}
                    <div className="flex">
                      <div className="flex-shrink-0 mr-4">
                        <div className="w-12 h-12 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center">
                          <BarChart className="h-6 w-6 text-blue-500" />
                        </div>
                      </div>
                      <div className="flex-grow">
                        <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-4">Measurable Outcomes</h3>
                        <div className="grid grid-cols-2 sm:grid-cols-3 gap-4">
                          {selectedCase.outcomes.map((outcome, index) => (
                            <motion.div 
                              key={index}
                              initial={{ opacity: 0, y: 20 }}
                              animate={{ opacity: 1, y: 0 }}
                              transition={{ delay: index * 0.1, type: 'spring' }}
                              className="bg-gray-50 dark:bg-gray-700/50 p-4 rounded-lg text-center"
                            >
                              <p className="text-2xl sm:text-3xl font-bold text-indigo-600 dark:text-indigo-400">{outcome.value}</p>
                              <p className="text-sm text-gray-600 dark:text-gray-300">{outcome.metric}</p>
                            </motion.div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </MotionWrapper>
  );
} 