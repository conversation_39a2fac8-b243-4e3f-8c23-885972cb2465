'use client';

import { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronLeft, ChevronRight, Database, PieChart, Smartphone, Layers, GitBranch, FileText, ShoppingCart, Truck, Star, Zap } from 'lucide-react';
import { useScrollReveal, useMagneticEffect, useStaggerAnimation } from '../animations/GSAPAnimations';

interface Skill {
  name: string;
  level: number; // 1-5
  category: string;
  description?: string;
}

interface Category {
  name: string;
  icon: React.ReactNode;
  color: string;
  skills: Skill[];
  description?: string;
}

const skillCategories: Category[] = [
  {
    name: 'Software Development',
    icon: <Smartphone className="w-6 h-6" />,
    color: 'from-purple-500 to-indigo-600',
    description: 'Experience with mobile app development and e-commerce platforms.',
    skills: [
      { name: 'Flutter App Development', level: 2, category: 'Software Development' },
      { name: 'Shopify E-commerce Development', level: 3, category: 'Software Development' },
      { name: 'React Native', level: 3, category: 'Software Development' },
      { name: 'Progressive Web Apps', level: 4, category: 'Software Development' },
    ],
  },
  {
    name: 'Data Analysis',
    icon: <PieChart className="w-6 h-6" />,
    color: 'from-green-500 to-teal-600',
    description: 'Proficient in data visualization and business intelligence tools.',
    skills: [
      { name: 'Power BI', level: 4, category: 'Data Analysis' },
      { name: 'Excel Advanced Functions', level: 5, category: 'Data Analysis' },
      { name: 'SQL Analytics', level: 4, category: 'Data Analysis' },
      { name: 'Tableau', level: 3, category: 'Data Analysis' },
    ],
  },
  {
    name: 'Project Management',
    icon: <Layers className="w-6 h-6" />,
    color: 'from-orange-500 to-red-600',
    description: 'Experienced in agile methodologies and team leadership.',
    skills: [
      { name: 'Agile/Scrum Methodology', level: 5, category: 'Project Management' },
      { name: 'Team Leadership', level: 5, category: 'Project Management' },
      { name: 'Risk Management', level: 4, category: 'Project Management' },
      { name: 'Stakeholder Management', level: 4, category: 'Project Management' },
    ],
  },
  {
    name: 'Technical Documentation',
    icon: <FileText className="w-6 h-6" />,
    color: 'from-blue-500 to-cyan-600',
    description: 'Skilled in creating comprehensive technical documentation.',
    skills: [
      { name: 'API Documentation', level: 4, category: 'Technical Documentation' },
      { name: 'Process Documentation', level: 5, category: 'Technical Documentation' },
      { name: 'User Manuals', level: 4, category: 'Technical Documentation' },
      { name: 'Technical Writing', level: 4, category: 'Technical Documentation' },
    ],
  },
  {
    name: 'E-commerce Platforms',
    icon: <ShoppingCart className="w-6 h-6" />,
    color: 'from-pink-500 to-rose-600',
    description: 'Expert in various e-commerce platforms and integrations.',
    skills: [
      { name: 'Shopify Development', level: 4, category: 'E-commerce Platforms' },
      { name: 'WooCommerce', level: 3, category: 'E-commerce Platforms' },
      { name: 'Payment Gateway Integration', level: 4, category: 'E-commerce Platforms' },
      { name: 'Inventory Management', level: 5, category: 'E-commerce Platforms' },
    ],
  },
  {
    name: 'Logistics Systems',
    icon: <Truck className="w-6 h-6" />,
    color: 'from-indigo-500 to-purple-600',
    description: 'Skilled in logistics tracking systems and delivery management.',
    skills: [
      { name: 'Tookan (Tracking System)', level: 5, category: 'Logistics Systems' },
      { name: 'JungleWorks', level: 4, category: 'Logistics Systems' },
      { name: 'Route Optimization', level: 4, category: 'Logistics Systems' },
      { name: 'Fleet Management', level: 4, category: 'Logistics Systems' },
    ],
  },
];

export default function HorizontalSkillsGrid() {
  const [activeCategory, setActiveCategory] = useState<Category | null>(skillCategories[0]);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(true);

  // GSAP animations
  useScrollReveal('.skill-category-card', {
    from: { opacity: 0, y: 50, rotationY: -15 },
    to: { opacity: 1, y: 0, rotationY: 0, duration: 0.8 }
  });
  
  useStaggerAnimation('.skill-item', {
    from: { opacity: 0, x: -30 },
    to: { opacity: 1, x: 0, duration: 0.6 }
  }, 0.1);
  
  useMagneticEffect('.magnetic-category', 0.15);

  const checkScrollButtons = () => {
    if (scrollContainerRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = scrollContainerRef.current;
      setCanScrollLeft(scrollLeft > 0);
      setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 1);
    }
  };

  const scrollLeft = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: -300, behavior: 'smooth' });
    }
  };

  const scrollRight = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: 300, behavior: 'smooth' });
    }
  };

  useEffect(() => {
    const container = scrollContainerRef.current;
    if (container) {
      container.addEventListener('scroll', checkScrollButtons);
      checkScrollButtons();
      return () => container.removeEventListener('scroll', checkScrollButtons);
    }
  }, []);

  const renderStars = (level: number) => {
    return Array.from({ length: 5 }).map((_, i) => (
      <Star
        key={i}
        className={`w-4 h-4 ${
          i < level 
            ? 'text-yellow-500 fill-yellow-500' 
            : 'text-gray-300 dark:text-gray-600'
        }`}
      />
    ));
  };

  return (
    <div className="space-y-8">
      {/* Section Header */}
      <div className="text-center">
        <h2 className="section-title">Skills & Expertise</h2>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          Explore my technical skills and professional expertise across various domains.
        </p>
      </div>

      {/* Horizontal Categories Scroll */}
      <div className="relative">
        {/* Left Scroll Button */}
        {canScrollLeft && (
          <button
            onClick={scrollLeft}
            className="scroll-indicator scroll-indicator-left text-white"
          >
            <ChevronLeft className="h-6 w-6" />
          </button>
        )}

        {/* Right Scroll Button */}
        {canScrollRight && (
          <button
            onClick={scrollRight}
            className="scroll-indicator scroll-indicator-right text-white"
          >
            <ChevronRight className="h-6 w-6" />
          </button>
        )}

        {/* Categories Container */}
        <div
          ref={scrollContainerRef}
          className="horizontal-scroll py-4"
          style={{ paddingLeft: '1rem', paddingRight: '1rem' }}
        >
          {skillCategories.map((category, index) => (
            <motion.div
              key={category.name}
              className="horizontal-scroll-item skill-category-card magnetic-category"
              style={{ width: '280px' }}
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <motion.button
                onClick={() => setActiveCategory(category)}
                className={`w-full h-full min-h-[160px] rounded-2xl p-6 text-white transition-all duration-500 shadow-lg border border-white/20 ${
                  activeCategory?.name === category.name 
                    ? 'ring-2 ring-white/50 ring-offset-2 ring-offset-primary shadow-2xl scale-105' 
                    : 'opacity-90 hover:opacity-100 hover:shadow-xl hover:scale-102'
                }`}
                whileHover={{ scale: 1.03, transition: { duration: 0.2 } }}
                whileTap={{ scale: 0.98 }}
                style={{
                  background: `linear-gradient(135deg, ${category.color.split(' ')[0].replace('from-', '')} 0%, ${category.color.split(' ')[1].replace('to-', '')} 100%)`,
                }}
              >
                <div className="flex flex-col items-center text-center h-full">
                  <div className="p-4 bg-white/20 rounded-full mb-4 backdrop-blur-sm">
                    {category.icon}
                  </div>
                  <h3 className="text-lg font-bold mb-2">{category.name}</h3>
                  <p className="text-sm opacity-90 line-clamp-2">{category.description}</p>
                  <div className="mt-auto pt-3">
                    <span className="text-xs bg-white/20 px-3 py-1 rounded-full">
                      {category.skills.length} skills
                    </span>
                  </div>
                </div>
              </motion.button>
            </motion.div>
          ))}
        </div>
      </div>

      {/* Skills Display */}
      <AnimatePresence mode="wait">
        {activeCategory && (
          <motion.div
            key={activeCategory.name}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.5 }}
            className="mt-8"
          >
            <div className="glass p-8 rounded-2xl">
              <div className="flex items-center mb-6">
                <div 
                  className="p-4 rounded-full mr-4 text-white"
                  style={{
                    background: `linear-gradient(135deg, ${activeCategory.color.split(' ')[0].replace('from-', '')} 0%, ${activeCategory.color.split(' ')[1].replace('to-', '')} 100%)`,
                  }}
                >
                  {activeCategory.icon}
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-foreground">{activeCategory.name}</h3>
                  <p className="text-muted-foreground mt-1">
                    {activeCategory.description}
                  </p>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {activeCategory.skills.map((skill, index) => (
                  <motion.div
                    key={skill.name}
                    className="skill-item p-4 rounded-xl border border-border/50 bg-card/50 backdrop-blur-sm hover:bg-card/80 transition-all duration-300"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                    whileHover={{ y: -2, scale: 1.02 }}
                  >
                    <div className="flex justify-between items-start mb-3">
                      <h4 className="font-semibold text-foreground">{skill.name}</h4>
                      <div className="flex items-center gap-1">
                        <Zap className="h-4 w-4 text-yellow-500" />
                        <span className="text-sm font-medium text-muted-foreground">
                          {skill.level}/5
                        </span>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-1 mb-2">
                      {renderStars(skill.level)}
                    </div>
                    
                    <div className="w-full bg-muted rounded-full h-2">
                      <motion.div
                        className="h-2 rounded-full bg-gradient-to-r from-primary to-accent"
                        initial={{ width: 0 }}
                        animate={{ width: `${(skill.level / 5) * 100}%` }}
                        transition={{ duration: 1, delay: index * 0.1 }}
                      />
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}
