'use client';

import React, { ReactNode } from 'react';
import { motion } from 'framer-motion';

interface BlobBackgroundProps {
  children: ReactNode;
  className?: string;
  variant?: 'primary' | 'secondary' | 'accent';
}

const BlobBackground: React.FC<BlobBackgroundProps> = ({ 
  children, 
  className = '',
  variant = 'primary'
}) => {
  // Color variants
  const colorVariants = {
    primary: {
      blob1: 'bg-primary/30',
      blob2: 'bg-accent/30',
      blob3: 'bg-primary/20'
    },
    secondary: {
      blob1: 'bg-secondary/40',
      blob2: 'bg-primary/20',
      blob3: 'bg-accent/20'
    },
    accent: {
      blob1: 'bg-accent/30',
      blob2: 'bg-primary/20',
      blob3: 'bg-secondary/30'
    }
  };

  const colors = colorVariants[variant];

  return (
    <div className={`relative overflow-hidden ${className}`}>
      <div className="absolute inset-0 overflow-hidden -z-10">
        <motion.div 
          className={`absolute top-0 -left-4 w-72 h-72 ${colors.blob1} rounded-full mix-blend-multiply filter blur-xl opacity-70`}
          animate={{ 
            x: [0, 30, -20, 0],
            y: [0, -50, 20, 0],
            scale: [1, 1.1, 0.9, 1]
          }}
          transition={{ 
            duration: 20,
            repeat: Infinity,
            repeatType: "reverse"
          }}
        />
        <motion.div 
          className={`absolute top-0 -right-4 w-72 h-72 ${colors.blob2} rounded-full mix-blend-multiply filter blur-xl opacity-70`}
          animate={{ 
            x: [0, -30, 20, 0],
            y: [0, 50, -20, 0],
            scale: [1, 0.9, 1.1, 1]
          }}
          transition={{ 
            duration: 25,
            repeat: Infinity,
            repeatType: "reverse"
          }}
        />
        <motion.div 
          className={`absolute -bottom-8 left-20 w-72 h-72 ${colors.blob3} rounded-full mix-blend-multiply filter blur-xl opacity-70`}
          animate={{ 
            x: [0, 20, -30, 0],
            y: [0, -20, 50, 0],
            scale: [1, 1.1, 0.9, 1]
          }}
          transition={{ 
            duration: 30,
            repeat: Infinity,
            repeatType: "reverse"
          }}
        />
      </div>
      {children}
    </div>
  );
};

export default BlobBackground; 