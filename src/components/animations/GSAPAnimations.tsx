'use client';

import { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

// Register GSAP plugins
if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger);
}

// GSAP Timeline Animation Hook
export const useGSAPTimeline = () => {
  const timelineRef = useRef<gsap.core.Timeline | null>(null);

  useEffect(() => {
    timelineRef.current = gsap.timeline({ paused: true });
    return () => {
      timelineRef.current?.kill();
    };
  }, []);

  return timelineRef.current;
};

// Scroll-triggered reveal animation
export const useScrollReveal = (trigger: string, options?: {
  from?: gsap.TweenVars;
  to?: gsap.TweenVars;
  scrollTrigger?: ScrollTrigger.Vars;
}) => {
  useEffect(() => {
    const elements = gsap.utils.toArray(trigger);

    elements.forEach((element: any) => {
      gsap.fromTo(element,
        {
          opacity: 0,
          y: 100,
          scale: 0.8,
          ...(options?.from || {})
        },
        {
          opacity: 1,
          y: 0,
          scale: 1,
          duration: 1.2,
          ease: "power3.out",
          scrollTrigger: {
            trigger: element,
            start: "top 80%",
            end: "bottom 20%",
            toggleActions: "play none none reverse",
            ...(options?.scrollTrigger || {})
          },
          ...(options?.to || {})
        }
      );
    });

    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill());
    };
  }, [trigger, options]);
};

// Parallax effect hook
export const useParallax = (trigger: string, speed: number = 0.5) => {
  useEffect(() => {
    const elements = gsap.utils.toArray(trigger);
    
    elements.forEach((element: any) => {
      gsap.to(element, {
        yPercent: -50 * speed,
        ease: "none",
        scrollTrigger: {
          trigger: element,
          start: "top bottom",
          end: "bottom top",
          scrub: true
        }
      });
    });

    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill());
    };
  }, [trigger, speed]);
};

// SVG path animation
export const useSVGPathAnimation = (pathSelector: string, options?: {
  duration?: number;
  delay?: number;
  ease?: string;
}) => {
  useEffect(() => {
    const paths = gsap.utils.toArray(pathSelector);
    
    paths.forEach((path: any) => {
      const length = path.getTotalLength();
      
      gsap.set(path, {
        strokeDasharray: length,
        strokeDashoffset: length
      });
      
      gsap.to(path, {
        strokeDashoffset: 0,
        duration: options?.duration || 2,
        delay: options?.delay || 0,
        ease: options?.ease || "power2.inOut",
        scrollTrigger: {
          trigger: path,
          start: "top 80%",
          toggleActions: "play none none reverse"
        }
      });
    });

    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill());
    };
  }, [pathSelector, options]);
};

// Stagger animation for multiple elements
export const useStaggerAnimation = (
  selector: string,
  animation: {
    from?: gsap.TweenVars;
    to?: gsap.TweenVars;
  },
  stagger: number = 0.1
) => {
  useEffect(() => {
    const elements = gsap.utils.toArray(selector);

    gsap.fromTo(elements,
      {
        opacity: 0,
        y: 50,
        scale: 0.8,
        ...(animation.from || {})
      },
      {
        opacity: 1,
        y: 0,
        scale: 1,
        duration: 0.8,
        ease: "power3.out",
        stagger: stagger,
        scrollTrigger: {
          trigger: elements[0] as Element,
          start: "top 80%",
          toggleActions: "play none none reverse"
        },
        ...(animation.to || {})
      }
    );

    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill());
    };
  }, [selector, animation, stagger]);
};

// Magnetic effect for interactive elements
export const useMagneticEffect = (selector: string, strength: number = 0.3) => {
  useEffect(() => {
    const elements = gsap.utils.toArray(selector);
    
    elements.forEach((element: any) => {
      const handleMouseMove = (e: MouseEvent) => {
        const rect = element.getBoundingClientRect();
        const x = e.clientX - rect.left - rect.width / 2;
        const y = e.clientY - rect.top - rect.height / 2;
        
        gsap.to(element, {
          x: x * strength,
          y: y * strength,
          duration: 0.3,
          ease: "power2.out"
        });
      };
      
      const handleMouseLeave = () => {
        gsap.to(element, {
          x: 0,
          y: 0,
          duration: 0.5,
          ease: "elastic.out(1, 0.3)"
        });
      };
      
      element.addEventListener('mousemove', handleMouseMove);
      element.addEventListener('mouseleave', handleMouseLeave);
      
      return () => {
        element.removeEventListener('mousemove', handleMouseMove);
        element.removeEventListener('mouseleave', handleMouseLeave);
      };
    });
  }, [selector, strength]);
};

// Text reveal animation
export const useTextReveal = (selector: string, options?: {
  duration?: number;
  stagger?: number;
  ease?: string;
}) => {
  useEffect(() => {
    const elements = gsap.utils.toArray(selector);
    
    elements.forEach((element: any) => {
      const text = element.textContent;
      const chars = text.split('');
      
      element.innerHTML = chars.map((char: string) => 
        `<span style="display: inline-block;">${char === ' ' ? '&nbsp;' : char}</span>`
      ).join('');
      
      const spans = element.querySelectorAll('span');
      
      gsap.fromTo(spans,
        {
          opacity: 0,
          y: 100,
          rotationX: -90
        },
        {
          opacity: 1,
          y: 0,
          rotationX: 0,
          duration: options?.duration || 0.8,
          ease: options?.ease || "back.out(1.7)",
          stagger: options?.stagger || 0.02,
          scrollTrigger: {
            trigger: element,
            start: "top 80%",
            toggleActions: "play none none reverse"
          }
        }
      );
    });

    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill());
    };
  }, [selector, options]);
};

// Loading animation component
export const LoadingSpinner = ({ className = "" }: { className?: string }) => {
  const spinnerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (spinnerRef.current) {
      gsap.to(spinnerRef.current, {
        rotation: 360,
        duration: 1,
        ease: "none",
        repeat: -1
      });
    }
  }, []);

  return (
    <div ref={spinnerRef} className={`w-8 h-8 border-2 border-primary border-t-transparent rounded-full ${className}`} />
  );
};

// Floating animation component
export const FloatingElement = ({ 
  children, 
  className = "",
  intensity = 10,
  duration = 3
}: { 
  children: React.ReactNode;
  className?: string;
  intensity?: number;
  duration?: number;
}) => {
  const elementRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (elementRef.current) {
      gsap.to(elementRef.current, {
        y: -intensity,
        duration: duration,
        ease: "power2.inOut",
        repeat: -1,
        yoyo: true
      });
    }
  }, [intensity, duration]);

  return (
    <div ref={elementRef} className={className}>
      {children}
    </div>
  );
};
