'use client';

import { ReactNode } from 'react';
import { motion } from 'framer-motion';
import { cardHover } from './motion';

interface AnimatedCardProps {
  children: ReactNode;
  className?: string;
  delay?: number;
  hoverEffect?: boolean;
  onClick?: () => void;
}

const AnimatedCard = ({
  children,
  className = '',
  delay = 0,
  hoverEffect = true,
  onClick
}: AnimatedCardProps) => {
  return (
    <motion.div
      className={`overflow-hidden rounded-xl ${className}`}
      initial={{ opacity: 0, y: 50 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{
        type: 'spring',
        damping: 20,
        stiffness: 100,
        delay
      }}
      viewport={{ once: true, amount: 0.2 }}
      whileHover={hoverEffect ? { 
        y: -8, 
        boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
        transition: { type: 'spring', stiffness: 400, damping: 10 }
      } : undefined}
      onClick={onClick}
    >
      {children}
    </motion.div>
  );
};

// Variant with 3D tilt effect
export const TiltCard = ({
  children,
  className = '',
  delay = 0,
  onClick
}: AnimatedCardProps) => {
  return (
    <motion.div
      className={`overflow-hidden rounded-xl ${className}`}
      initial={{ opacity: 0, y: 50 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{
        type: 'spring',
        damping: 20,
        stiffness: 100,
        delay
      }}
      viewport={{ once: true, amount: 0.2 }}
      whileHover={{ 
        rotateX: 5, 
        rotateY: 5,
        y: -8, 
        boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
        transition: { type: 'spring', stiffness: 400, damping: 10 }
      }}
      style={{ perspective: 1000 }}
      onClick={onClick}
    >
      {children}
    </motion.div>
  );
};

// Variant with glowing effect
export const GlowCard = ({
  children,
  className = '',
  delay = 0,
  onClick
}: AnimatedCardProps) => {
  return (
    <motion.div
      className={`overflow-hidden rounded-xl relative ${className}`}
      initial={{ opacity: 0, y: 50 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{
        type: 'spring',
        damping: 20,
        stiffness: 100,
        delay
      }}
      viewport={{ once: true, amount: 0.2 }}
      whileHover={{ 
        y: -8,
        transition: { type: 'spring', stiffness: 400, damping: 10 }
      }}
      onClick={onClick}
    >
      <motion.div
        className="absolute inset-0 -z-10 bg-gradient-to-r from-indigo-500 via-purple-500 to-pink-500 opacity-0 blur-xl"
        whileHover={{ 
          opacity: 0.7,
          scale: 1.1,
          transition: { duration: 0.3 }
        }}
      />
      {children}
    </motion.div>
  );
};

export default AnimatedCard; 