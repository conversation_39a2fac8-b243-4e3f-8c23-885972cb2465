'use client';

import React, { ReactNode } from 'react';
import { motion } from 'framer-motion';
import { fadeIn, staggerContainer } from './motion';

interface MotionWrapperProps {
  children: ReactNode;
  className?: string;
  delay?: number;
}

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.3,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      type: 'spring',
      stiffness: 260,
      damping: 20,
    },
  },
};

// Main wrapper component for sections
export const MotionWrapper: React.FC<MotionWrapperProps> = ({
  children,
  className = '',
  delay = 0,
}) => {
  return (
    <motion.div
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, margin: '-100px' }}
      variants={{
        hidden: { opacity: 0 },
        visible: {
          opacity: 1,
          transition: {
            delayChildren: delay,
            staggerChildren: 0.1,
          },
        },
      }}
      className={className}
    >
      {children}
    </motion.div>
  );
};

// Motion component for fade-in animations
export const MotionFade: React.FC<MotionWrapperProps> = ({
  children,
  className = '',
  delay = 0,
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{
        duration: 0.6,
        delay,
        ease: [0.22, 1, 0.36, 1],
      }}
      className={className}
    >
      {children}
    </motion.div>
  );
};

// Motion component for scale animations
export const MotionScale: React.FC<MotionWrapperProps> = ({
  children,
  className = '',
  delay = 0,
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      whileInView={{ opacity: 1, scale: 1 }}
      viewport={{ once: true }}
      transition={{
        duration: 0.6,
        delay,
        ease: [0.22, 1, 0.36, 1],
      }}
      className={className}
    >
      {children}
    </motion.div>
  );
};

// Motion component for slide-in animations
export const MotionSlide: React.FC<MotionWrapperProps & { direction?: 'left' | 'right' | 'up' | 'down' }> = ({
  children,
  className = '',
  delay = 0,
  direction = 'up',
}) => {
  const directionVariants = {
    up: { y: 50, x: 0 },
    down: { y: -50, x: 0 },
    left: { x: 50, y: 0 },
    right: { x: -50, y: 0 },
  };

  return (
    <motion.div
      initial={{ opacity: 0, ...directionVariants[direction] }}
      whileInView={{ opacity: 1, x: 0, y: 0 }}
      viewport={{ once: true }}
      transition={{
        duration: 0.8,
        delay,
        type: 'spring',
        stiffness: 100,
        damping: 20,
      }}
      className={className}
    >
      {children}
    </motion.div>
  );
};

// This component handles text animations
export const MotionText = ({ 
  children, 
  className = '', 
  delay = 0 
}: { 
  children: string, 
  className?: string, 
  delay?: number 
}) => {
  return (
    <motion.p
      variants={fadeIn('up', delay)}
      initial="hidden"
      whileInView="show"
      viewport={{ once: true, amount: 0.25 }}
      className={className}
    >
      {children}
    </motion.p>
  );
};

// This component creates a parallax effect
export const MotionParallax = ({ 
  children, 
  className = '', 
  offset = 50 
}: { 
  children: ReactNode, 
  className?: string, 
  offset?: number
}) => {
  return (
    <motion.div
      initial={{ y: 0 }}
      style={{ y: 0 }}
      whileInView={{
        y: [-offset, offset],
        transition: {
          repeat: Infinity,
          repeatType: "reverse",
          duration: 8,
          ease: "easeInOut"
        }
      }}
      className={className}
    >
      {children}
    </motion.div>
  );
};

// This component creates a 3D tilt effect on hover
export const MotionTilt = ({ 
  children, 
  className = '' 
}: { 
  children: ReactNode, 
  className?: string 
}) => {
  return (
    <motion.div
      className={className}
      whileHover={{ 
        scale: 1.02,
        rotateX: 5, 
        rotateY: 5,
        transition: { type: 'spring', stiffness: 400, damping: 10 }
      }}
      style={{ perspective: 1000 }}
    >
      {children}
    </motion.div>
  );
};

export default MotionWrapper; 