'use client';

import React, { ReactNode, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { gsap } from 'gsap';

interface PageTransitionProps {
  children: ReactNode;
  className?: string;
  variant?: 'slide' | 'fade' | 'scale' | 'curtain' | 'reveal';
}

const pageVariants = {
  slide: {
    initial: { opacity: 0, x: 100, scale: 0.98 },
    in: { opacity: 1, x: 0, scale: 1 },
    out: { opacity: 0, x: -100, scale: 1.02 },
  },
  fade: {
    initial: { opacity: 0, y: 20 },
    in: { opacity: 1, y: 0 },
    out: { opacity: 0, y: -20 },
  },
  scale: {
    initial: { opacity: 0, scale: 0.8, rotateX: -15 },
    in: { opacity: 1, scale: 1, rotateX: 0 },
    out: { opacity: 0, scale: 1.1, rotateX: 15 },
  },
  curtain: {
    initial: { opacity: 0, clipPath: 'inset(0 100% 0 0)' },
    in: { opacity: 1, clipPath: 'inset(0 0% 0 0)' },
    out: { opacity: 0, clipPath: 'inset(0 0 0 100%)' },
  },
  reveal: {
    initial: { opacity: 0, y: 100, skewY: 5 },
    in: { opacity: 1, y: 0, skewY: 0 },
    out: { opacity: 0, y: -100, skewY: -5 },
  },
};

const pageTransition = {
  type: 'spring',
  stiffness: 260,
  damping: 20,
  duration: 0.8,
};

// Enhanced Page Transition with GSAP
const PageTransition: React.FC<PageTransitionProps> = ({
  children,
  className = '',
  variant = 'fade'
}) => {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (containerRef.current) {
      // Add entrance animation with GSAP
      gsap.fromTo(containerRef.current.children,
        {
          opacity: 0,
          y: 50,
          stagger: 0.1,
        },
        {
          opacity: 1,
          y: 0,
          duration: 0.8,
          ease: "power3.out",
          stagger: 0.1,
        }
      );
    }
  }, []);

  return (
    <motion.div
      ref={containerRef}
      initial="initial"
      animate="in"
      exit="out"
      variants={pageVariants[variant]}
      transition={pageTransition}
      className={className}
    >
      {children}
    </motion.div>
  );
};

// Loading Transition Component
export const LoadingTransition = ({ isLoading }: { isLoading: boolean }) => {
  return (
    <AnimatePresence>
      {isLoading && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 flex items-center justify-center bg-background/80 backdrop-blur-sm"
        >
          <motion.div
            className="flex flex-col items-center space-y-4"
            initial={{ scale: 0.8, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.8, opacity: 0 }}
          >
            <motion.div
              className="w-16 h-16 border-4 border-primary border-t-transparent rounded-full"
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
            />
            <motion.p
              className="text-lg font-medium text-foreground"
              animate={{ opacity: [0.5, 1, 0.5] }}
              transition={{ duration: 1.5, repeat: Infinity }}
            >
              Loading...
            </motion.p>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

// Section Transition Component
export const SectionTransition = ({
  children,
  delay = 0,
  className = ""
}: {
  children: ReactNode;
  delay?: number;
  className?: string;
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 60 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true, margin: "-100px" }}
      transition={{
        duration: 0.8,
        delay,
        ease: [0.22, 1, 0.36, 1],
      }}
      className={className}
    >
      {children}
    </motion.div>
  );
};

export default PageTransition;