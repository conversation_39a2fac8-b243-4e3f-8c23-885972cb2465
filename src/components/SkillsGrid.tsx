'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Database, PieChart, Smartphone, Layers, GitBranch, FileText, ShoppingCart, Truck, Star, ChevronRight, Zap } from 'lucide-react';
import { MotionWrapper, MotionFade } from './animations/MotionWrapper';
import { GlowCard } from './animations/AnimatedCard';

interface Skill {
  name: string;
  level: number; // 1-5
  category: string;
  description?: string;
}

interface Category {
  name: string;
  icon: React.ReactNode;
  color: string;
  skills: Skill[];
  description?: string;
}

const skillCategories: Category[] = [
  {
    name: 'Technical Skills',
    icon: <Smartphone className="w-6 h-6" />,
    color: 'from-purple-500 to-indigo-600',
    description: 'Comprehensive technical skills including mobile development, system development, and testing methodologies.',
    skills: [
      { name: 'Flutter App Development', level: 4, category: 'Technical Skills', description: 'Designed, coded, and tested Flutter-based delivery apps with real-time tracking capabilities' },
      { name: 'Shopify E-commerce Development', level: 5, category: 'Technical Skills', description: 'Developed complete Shopify websites increasing customer orders by 25%' },
      { name: 'System Development', level: 5, category: 'Technical Skills', description: 'Expert in designing custom enterprise systems to meet business needs' },
      { name: 'User Acceptance Testing (UAT)', level: 4, category: 'Technical Skills', description: 'Design and execute testing protocols to validate products meet business requirements' },
      { name: 'Android Studio', level: 3, category: 'Technical Skills', description: 'Proficient with Android Studio IDE for mobile app development' },
      { name: 'VSC (Visual Studio Code)', level: 4, category: 'Technical Skills', description: 'Advanced usage of Visual Studio Code for development' },
      { name: 'Firebase & Firestore Products', level: 4, category: 'Technical Skills', description: 'Experienced with Firebase backend services and Firestore database' },
    ],
  },
  {
    name: 'Data & Analytics',
    icon: <Database className="w-6 h-6" />,
    color: 'from-blue-500 to-cyan-400',
    description: 'Advanced data management, analytics, and business intelligence capabilities for data-driven decision making.',
    skills: [
      { name: 'Data Collection', level: 5, category: 'Data & Analytics', description: 'Expert in gathering relevant data from multiple sources to inform business decisions' },
      { name: 'Data Forecasting', level: 5, category: 'Data & Analytics', description: 'Advanced skills in predictive modeling and demand forecasting' },
      { name: 'SQL (SQL - BigQuery)', level: 4, category: 'Data & Analytics', description: 'Proficient in writing complex SQL queries and BigQuery analytics' },
      { name: 'Google Data Studio', level: 5, category: 'Data & Analytics', description: 'Expert in creating interactive dashboards for data visualization' },
      { name: 'Looker (Data reporting System)', level: 4, category: 'Data & Analytics', description: 'Advanced knowledge of Looker for business intelligence' },
      { name: 'Microsoft Power BI', level: 4, category: 'Data & Analytics', description: 'Advanced skills in Power BI for business analytics and reporting' },
      { name: 'Amplitude', level: 4, category: 'Data & Analytics', description: 'Advanced usage for product analytics and user behavior tracking' },
    ],
  },
  {
    name: 'Product Management',
    icon: <PieChart className="w-6 h-6" />,
    color: 'from-green-500 to-emerald-600',
    description: 'Comprehensive product management skills including strategy, research, and agile methodologies.',
    skills: [
      { name: 'Scrum Methodology', level: 5, category: 'Product Management', description: 'Lead sprint planning, daily standups, and retrospectives using agile framework' },
      { name: 'User Research Methodologies', level: 4, category: 'Product Management', description: 'Execute qualitative and quantitative research to gather user insights' },
      { name: 'Product Roadmap Development', level: 5, category: 'Product Management', description: 'Create strategic timeline-based plans that align product features with business objectives' },
      { name: 'Requirements Specification Writing', level: 5, category: 'Product Management', description: 'Document detailed functional and technical requirements for development teams' },
      { name: 'Usability Testing', level: 4, category: 'Product Management', description: 'Conduct user testing sessions to evaluate product usability' },
      { name: 'Product Prioritization', level: 5, category: 'Product Management', description: 'Use data-driven approaches to rank features based on user value and business impact' },
    ],
  },
  {
    name: 'Tools & Software',
    icon: <Layers className="w-6 h-6" />,
    color: 'from-orange-500 to-amber-600',
    description: 'Extensive experience with development tools, ERP systems, and business software platforms.',
    skills: [
      { name: 'ERP (Edara - ERP Next - Odoo)', level: 5, category: 'Tools & Software', description: 'Expert in multiple ERP systems implementation and customization' },
      { name: 'Jira', level: 5, category: 'Tools & Software', description: 'Expert in using Jira for backlog, sprints, epics, and stories management' },
      { name: 'MS Office (Excel - Office - PowerPoint)', level: 5, category: 'Tools & Software', description: 'Expert in Microsoft Office applications for business use' },
      { name: 'G-Suite (Google Administration)', level: 5, category: 'Tools & Software', description: 'Expert in administering G-Suite for organizational efficiency' },
      { name: 'Shopify Admin', level: 5, category: 'Tools & Software', description: 'Expert in Shopify platform administration and customization' },
      { name: 'Google Tag Manager', level: 4, category: 'Tools & Software', description: 'Advanced skills in implementing tracking and analytics' },
      { name: 'Tookan (Tracking System)', level: 5, category: 'Tools & Software', description: 'Expert in implementing and optimizing delivery tracking systems' },
    ],
  },
  {
    name: 'Communication & Collaboration',
    icon: <GitBranch className="w-6 h-6" />,
    color: 'from-red-500 to-rose-600',
    description: 'Advanced communication tools and collaboration platforms for effective team management.',
    skills: [
      { name: 'Slack / Microsoft Teams', level: 5, category: 'Communication & Collaboration', description: 'Expert in team communication and collaboration platforms' },
      { name: 'Zoom / Google Meet', level: 5, category: 'Communication & Collaboration', description: 'Advanced skills in video conferencing and remote collaboration' },
      { name: 'Figma / FigJam', level: 4, category: 'Communication & Collaboration', description: 'Advanced design collaboration and wireframe creation' },
      { name: 'Notion', level: 4, category: 'Communication & Collaboration', description: 'Advanced centralized knowledge and documentation management' },
      { name: 'Asana', level: 4, category: 'Communication & Collaboration', description: 'Advanced task and project management capabilities' },
      { name: 'GitHub', level: 3, category: 'Communication & Collaboration', description: 'Intermediate version control and development collaboration' },
    ],
  },
  {
    name: 'User Research & Feedback',
    icon: <FileText className="w-6 h-6" />,
    color: 'from-teal-500 to-green-600',
    description: 'Comprehensive user research and feedback collection tools for product optimization.',
    skills: [
      { name: 'Google Forms', level: 5, category: 'User Research & Feedback', description: 'Expert in creating user surveys and feedback collection' },
      { name: 'Hotjar', level: 4, category: 'User Research & Feedback', description: 'Advanced session recording and heatmap analysis' },
      { name: 'Maze', level: 3, category: 'User Research & Feedback', description: 'Intermediate remote usability testing capabilities' },
      { name: 'Zendesk', level: 4, category: 'User Research & Feedback', description: 'Advanced user conversations and feedback tracking' },
    ],
  },
  {
    name: 'Strategy & Analysis',
    icon: <ShoppingCart className="w-6 h-6" />,
    color: 'from-indigo-500 to-purple-600',
    description: 'Strategic planning and competitive analysis tools for market intelligence and business strategy.',
    skills: [
      { name: 'Gartner / CB Insights / G2', level: 4, category: 'Strategy & Analysis', description: 'Advanced market analysis and competitive intelligence' },
      { name: 'Google Trends / SEMrush / Ahrefs', level: 4, category: 'Strategy & Analysis', description: 'Advanced demand and keyword analysis capabilities' },
      { name: 'Product Hunt / Betalist', level: 3, category: 'Strategy & Analysis', description: 'Intermediate market trend spotting and product discovery' },
      { name: 'Craft.io', level: 4, category: 'Strategy & Analysis', description: 'Advanced strategy and planning with product hierarchy' },
      { name: 'Napkin AI', level: 3, category: 'Strategy & Analysis', description: 'Intermediate AI-powered strategic planning and analysis' },
    ],
  },
];

export default function SkillsGrid() {
  const [activeCategory, setActiveCategory] = useState<Category | null>(skillCategories[0]);
  const [selectedSkill, setSelectedSkill] = useState<Skill | null>(null);
  
  return (
    <MotionWrapper className="space-y-8">
      {/* Category Selection UI */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {skillCategories.map((category, index) => (
          <motion.div
            key={category.name}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: index * 0.1 }}
            viewport={{ once: true, amount: 0.1 }}
            className="relative"
          >
            <motion.button
              onClick={() => setActiveCategory(category)}
              className={`w-full h-full min-h-[120px] rounded-xl p-5 text-white transition-all duration-300 shadow-md border border-white/20 ${
                activeCategory?.name === category.name 
                  ? 'ring-2 ring-white/50 ring-offset-2 ring-offset-indigo-500 shadow-lg scale-[1.02]' 
                  : 'opacity-90 hover:opacity-100 hover:shadow-md'
              }`}
              whileHover={{ scale: 1.03, transition: { duration: 0.2 } }}
              whileTap={{ scale: 0.98 }}
              style={{
                background: `linear-gradient(135deg, ${category.color.split(' ')[0].replace('from-', '')} 0%, ${category.color.split(' ')[1].replace('to-', '')} 100%)`,
              }}
            >
              <div className="flex flex-col items-center text-center">
                <div className="p-3 bg-white/20 rounded-full mb-3">
                  {category.icon}
                </div>
                <h3 className="text-sm md:text-base font-semibold">{category.name}</h3>
              </div>
            </motion.button>
          </motion.div>
        ))}
      </div>
      
      {/* Skills Display */}
      <AnimatePresence mode="wait">
        {activeCategory && (
          <motion.div
            key={activeCategory.name}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.5 }}
            className="mt-8"
          >
            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-md">
              <div className="flex items-center mb-4">
                <div className={`p-3 rounded-full mr-4 text-white bg-gradient-to-r ${activeCategory.color}`}>
                  {activeCategory.icon}
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white">{activeCategory.name}</h3>
                  <p className="text-gray-600 dark:text-gray-400 text-sm mt-1">
                    {activeCategory.description}
                  </p>
                </div>
              </div>
              
              <div className="mt-6 space-y-4">
                {activeCategory.skills.map((skill, index) => (
                  <motion.div
                    key={skill.name}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                    className="relative"
                  >
                    <motion.div
                      className={`p-4 rounded-lg border cursor-pointer transition-all ${
                        selectedSkill?.name === skill.name 
                          ? 'border-indigo-500 bg-indigo-50 dark:bg-indigo-900/20' 
                          : 'border-gray-200 dark:border-gray-700 hover:border-indigo-300 dark:hover:border-indigo-700 hover:bg-gray-50 dark:hover:bg-gray-800/50'
                      }`}
                      onClick={() => setSelectedSkill(selectedSkill?.name === skill.name ? null : skill)}
                      whileHover={{ y: -2 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <div className="flex justify-between items-center">
                        <div className="flex items-center space-x-3">
                          <div className={`w-2 h-10 rounded-full bg-gradient-to-b ${activeCategory.color}`}></div>
                          <div>
                            <h4 className="font-medium text-gray-900 dark:text-white">{skill.name}</h4>
                            {selectedSkill?.name === skill.name && skill.description && (
                              <motion.p
                                initial={{ opacity: 0, height: 0 }}
                                animate={{ opacity: 1, height: 'auto' }}
                                exit={{ opacity: 0, height: 0 }}
                                className="text-sm text-gray-600 dark:text-gray-400 mt-2"
                              >
                                {skill.description}
                              </motion.p>
                            )}
                          </div>
                        </div>
                        
                        <div className="flex items-center space-x-3">
                          <div className="flex">
                            {Array.from({ length: 5 }).map((_, i) => (
                              <Star
                                key={i}
                                className={`w-4 h-4 ${
                                  i < skill.level 
                                    ? 'text-yellow-500 fill-yellow-500' 
                                    : 'text-gray-300 dark:text-gray-600'
                                }`}
                              />
                            ))}
                          </div>
                          <ChevronRight className={`w-5 h-5 text-gray-400 transition-transform ${
                            selectedSkill?.name === skill.name ? 'rotate-90' : ''
                          }`} />
                        </div>
                      </div>
                    </motion.div>
                  </motion.div>
                ))}
              </div>
            </div>
          </motion.div>
        )}
        
        {!activeCategory && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-md text-center"
          >
            <div className="flex justify-center mb-4">
              <div className="p-4 bg-indigo-100 dark:bg-indigo-900/30 rounded-full">
                <Zap className="w-8 h-8 text-indigo-600 dark:text-indigo-400" />
              </div>
            </div>
            <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">Select a Skill Category</h3>
            <p className="text-gray-600 dark:text-gray-400 max-w-md mx-auto">
              Click on any category above to explore my skills and expertise in that area.
              Each category includes detailed proficiency levels and descriptions.
            </p>
          </motion.div>
        )}
      </AnimatePresence>
      
      {/* Skill Level Legend */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.5 }}
        viewport={{ once: true }}
        className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-md"
      >
        <h4 className="text-sm font-semibold uppercase tracking-wide text-gray-500 dark:text-gray-400 mb-3">Skill Level Legend</h4>
        <div className="flex flex-wrap gap-3">
          {[
            { label: 'Beginner', stars: 1, color: 'bg-yellow-400' },
            { label: 'Basic', stars: 2, color: 'bg-yellow-500' },
            { label: 'Intermediate', stars: 3, color: 'bg-green-500' },
            { label: 'Advanced', stars: 4, color: 'bg-blue-500' },
            { label: 'Expert', stars: 5, color: 'bg-indigo-600' },
          ].map((level) => (
            <div key={level.label} className="flex items-center space-x-2">
              <div className={`w-3 h-3 rounded-full ${level.color}`}></div>
              <div className="flex">
                {Array.from({ length: level.stars }).map((_, i) => (
                  <Star key={i} className="w-3 h-3 text-yellow-500 fill-yellow-500" />
                ))}
                {Array.from({ length: 5 - level.stars }).map((_, i) => (
                  <Star key={i} className="w-3 h-3 text-gray-300 dark:text-gray-600" />
                ))}
              </div>
              <span className="text-xs text-gray-600 dark:text-gray-400">{level.label}</span>
            </div>
          ))}
        </div>
      </motion.div>
    </MotionWrapper>
  );
} 