'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Database, PieChart, Smartphone, Layers, GitBranch, FileText, ShoppingCart, Truck, Star, ChevronRight, Zap } from 'lucide-react';
import { MotionWrapper, MotionFade } from './animations/MotionWrapper';
import { GlowCard } from './animations/AnimatedCard';

interface Skill {
  name: string;
  level: number; // 1-5
  category: string;
  description?: string;
}

interface Category {
  name: string;
  icon: React.ReactNode;
  color: string;
  skills: Skill[];
  description?: string;
}

const skillCategories: Category[] = [
  {
    name: 'Software Development',
    icon: <Smartphone className="w-6 h-6" />,
    color: 'from-purple-500 to-indigo-600',
    description: 'Experience with mobile app development and e-commerce platforms, focused on creating user-friendly solutions.',
    skills: [
      { name: 'Flutter App Development', level: 2, category: 'Software Development', description: 'Created cross-platform mobile applications using Flutter framework' },
      { name: 'Shopify E-commerce Development', level: 3, category: 'Software Development', description: 'Built and customized Shopify stores with theme modifications and app integrations' },
      { name: 'Android Studio', level: 1, category: 'Software Development', description: 'Familiar with Android Studio IDE for mobile app development' },
      { name: 'Flutter', level: 2, category: 'Software Development', description: 'Used Flutter framework for building natively compiled applications' },
      { name: 'Kotlin', level: 1, category: 'Software Development', description: 'Basic knowledge of Kotlin for Android app development' },
      { name: 'Java', level: 1, category: 'Software Development', description: 'Fundamental understanding of Java programming language' },
    ],
  },
  {
    name: 'Data Management',
    icon: <Database className="w-6 h-6" />,
    color: 'from-blue-500 to-cyan-400',
    description: 'Skilled in data collection, forecasting, and database management to drive informed business decisions.',
    skills: [
      { name: 'Data Collection', level: 5, category: 'Data Management', description: 'Expert in gathering relevant data from multiple sources to inform business decisions' },
      { name: 'Data Forecasting', level: 4, category: 'Data Management', description: 'Advanced skills in predictive modeling and trend analysis' },
      { name: 'SQL', level: 4, category: 'Data Management', description: 'Proficient in writing complex SQL queries for data analysis and management' },
      { name: 'MySQL', level: 4, category: 'Data Management', description: 'Experienced with MySQL database administration and optimization' },
      { name: 'Google Cloud Services', level: 3, category: 'Data Management', description: 'Intermediate knowledge of Google Cloud for data storage and processing' },
    ],
  },
  {
    name: 'Analytics & Reporting',
    icon: <PieChart className="w-6 h-6" />,
    color: 'from-green-500 to-emerald-600',
    description: 'Proficient in using various analytics tools to generate insights and create actionable reports.',
    skills: [
      { name: 'Amplitude', level: 4, category: 'Analytics & Reporting', description: 'Advanced usage of Amplitude for product analytics and user behavior tracking' },
      { name: 'Google Data Studio', level: 5, category: 'Analytics & Reporting', description: 'Expert in creating interactive dashboards for data visualization' },
      { name: 'Looker Streaming', level: 3, category: 'Analytics & Reporting', description: 'Intermediate knowledge of Looker for business intelligence and data exploration' },
      { name: 'Microsoft Power BI', level: 4, category: 'Analytics & Reporting', description: 'Advanced skills in Power BI for business analytics and reporting' },
      { name: 'Business Intelligence', level: 5, category: 'Analytics & Reporting', description: 'Expert in translating data into actionable business insights' },
    ],
  },
  {
    name: 'ERP & CRM Systems',
    icon: <Layers className="w-6 h-6" />,
    color: 'from-orange-500 to-amber-600',
    description: 'Experienced in implementing and optimizing ERP and CRM systems to streamline business processes.',
    skills: [
      { name: 'Edara', level: 4, category: 'ERP & CRM Systems', description: 'Advanced knowledge of Edara ERP system implementation and customization' },
      { name: 'ERP Next', level: 3, category: 'ERP & CRM Systems', description: 'Intermediate skills in ERP Next for business process management' },
      { name: 'Odoo', level: 4, category: 'ERP & CRM Systems', description: 'Advanced expertise in Odoo ERP implementation and module development' },
      { name: 'System Development', level: 5, category: 'ERP & CRM Systems', description: 'Expert in designing custom enterprise systems to meet business needs' },
      { name: 'Integration', level: 4, category: 'ERP & CRM Systems', description: 'Advanced skills in integrating different systems for seamless data flow' },
    ],
  },
  {
    name: 'Project Management',
    icon: <GitBranch className="w-6 h-6" />,
    color: 'from-red-500 to-rose-600',
    description: 'Strong project management skills using Agile methodologies and optimization techniques.',
    skills: [
      { name: 'Jira', level: 5, category: 'Project Management', description: 'Expert in using Jira for project tracking and team collaboration' },
      { name: 'Agile Methodology', level: 5, category: 'Project Management', description: 'Expert in implementing Agile frameworks for project delivery' },
      { name: 'Process Optimization', level: 5, category: 'Project Management', description: 'Expert in identifying and eliminating inefficiencies in business processes' },
    ],
  },
  {
    name: 'Productivity Tools',
    icon: <FileText className="w-6 h-6" />,
    color: 'from-teal-500 to-green-600',
    description: 'Highly proficient in various productivity tools to enhance efficiency and collaboration.',
    skills: [
      { name: 'MS Office (Excel, PowerPoint)', level: 5, category: 'Productivity Tools', description: 'Expert in Microsoft Office applications for business use' },
      { name: 'G-Suite (Google Administration)', level: 5, category: 'Productivity Tools', description: 'Expert in administering G-Suite for organizational efficiency' },
      { name: 'Google Products (Sheets, Docs, Slides)', level: 5, category: 'Productivity Tools', description: 'Expert in Google\'s productivity tools for collaboration' },
    ],
  },
  {
    name: 'E-commerce Platforms',
    icon: <ShoppingCart className="w-6 h-6" />,
    color: 'from-indigo-500 to-purple-600',
    description: 'Expert in Shopify and payment gateway integrations for e-commerce businesses.',
    skills: [
      { name: 'Shopify', level: 5, category: 'E-commerce Platforms', description: 'Expert in Shopify platform for e-commerce store development and management' },
      { name: 'Payment Gateway Integration', level: 4, category: 'E-commerce Platforms', description: 'Advanced skills in integrating various payment solutions into e-commerce sites' },
    ],
  },
  {
    name: 'Logistics Systems',
    icon: <Truck className="w-6 h-6" />,
    color: 'from-blue-600 to-indigo-700',
    description: 'Skilled in logistics tracking systems and delivery management solutions.',
    skills: [
      { name: 'Tookan (Tracking System)', level: 5, category: 'Logistics Systems', description: 'Expert in implementing and optimizing Tookan for delivery tracking' },
      { name: 'JungleWorks', level: 4, category: 'Logistics Systems', description: 'Advanced knowledge of JungleWorks logistics management platform' },
    ],
  },
];

export default function SkillsGrid() {
  const [activeCategory, setActiveCategory] = useState<Category | null>(skillCategories[0]);
  const [selectedSkill, setSelectedSkill] = useState<Skill | null>(null);
  
  return (
    <MotionWrapper className="space-y-8">
      {/* Category Selection UI */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {skillCategories.map((category, index) => (
          <motion.div
            key={category.name}
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: index * 0.1 }}
            viewport={{ once: true, amount: 0.1 }}
            className="relative"
          >
            <motion.button
              onClick={() => setActiveCategory(category)}
              className={`w-full h-full min-h-[120px] rounded-xl p-5 text-white transition-all duration-300 shadow-md border border-white/20 ${
                activeCategory?.name === category.name 
                  ? 'ring-2 ring-white/50 ring-offset-2 ring-offset-indigo-500 shadow-lg scale-[1.02]' 
                  : 'opacity-90 hover:opacity-100 hover:shadow-md'
              }`}
              whileHover={{ scale: 1.03, transition: { duration: 0.2 } }}
              whileTap={{ scale: 0.98 }}
              style={{
                background: `linear-gradient(135deg, ${category.color.split(' ')[0].replace('from-', '')} 0%, ${category.color.split(' ')[1].replace('to-', '')} 100%)`,
              }}
            >
              <div className="flex flex-col items-center text-center">
                <div className="p-3 bg-white/20 rounded-full mb-3">
                  {category.icon}
                </div>
                <h3 className="text-sm md:text-base font-semibold">{category.name}</h3>
              </div>
            </motion.button>
          </motion.div>
        ))}
      </div>
      
      {/* Skills Display */}
      <AnimatePresence mode="wait">
        {activeCategory && (
          <motion.div
            key={activeCategory.name}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.5 }}
            className="mt-8"
          >
            <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-md">
              <div className="flex items-center mb-4">
                <div className={`p-3 rounded-full mr-4 text-white bg-gradient-to-r ${activeCategory.color}`}>
                  {activeCategory.icon}
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900 dark:text-white">{activeCategory.name}</h3>
                  <p className="text-gray-600 dark:text-gray-400 text-sm mt-1">
                    {activeCategory.description}
                  </p>
                </div>
              </div>
              
              <div className="mt-6 space-y-4">
                {activeCategory.skills.map((skill, index) => (
                  <motion.div
                    key={skill.name}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                    className="relative"
                  >
                    <motion.div
                      className={`p-4 rounded-lg border cursor-pointer transition-all ${
                        selectedSkill?.name === skill.name 
                          ? 'border-indigo-500 bg-indigo-50 dark:bg-indigo-900/20' 
                          : 'border-gray-200 dark:border-gray-700 hover:border-indigo-300 dark:hover:border-indigo-700 hover:bg-gray-50 dark:hover:bg-gray-800/50'
                      }`}
                      onClick={() => setSelectedSkill(selectedSkill?.name === skill.name ? null : skill)}
                      whileHover={{ y: -2 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <div className="flex justify-between items-center">
                        <div className="flex items-center space-x-3">
                          <div className={`w-2 h-10 rounded-full bg-gradient-to-b ${activeCategory.color}`}></div>
                          <div>
                            <h4 className="font-medium text-gray-900 dark:text-white">{skill.name}</h4>
                            {selectedSkill?.name === skill.name && skill.description && (
                              <motion.p
                                initial={{ opacity: 0, height: 0 }}
                                animate={{ opacity: 1, height: 'auto' }}
                                exit={{ opacity: 0, height: 0 }}
                                className="text-sm text-gray-600 dark:text-gray-400 mt-2"
                              >
                                {skill.description}
                              </motion.p>
                            )}
                          </div>
                        </div>
                        
                        <div className="flex items-center space-x-3">
                          <div className="flex">
                            {Array.from({ length: 5 }).map((_, i) => (
                              <Star
                                key={i}
                                className={`w-4 h-4 ${
                                  i < skill.level 
                                    ? 'text-yellow-500 fill-yellow-500' 
                                    : 'text-gray-300 dark:text-gray-600'
                                }`}
                              />
                            ))}
                          </div>
                          <ChevronRight className={`w-5 h-5 text-gray-400 transition-transform ${
                            selectedSkill?.name === skill.name ? 'rotate-90' : ''
                          }`} />
                        </div>
                      </div>
                    </motion.div>
                  </motion.div>
                ))}
              </div>
            </div>
          </motion.div>
        )}
        
        {!activeCategory && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="bg-white dark:bg-gray-800 rounded-xl p-8 shadow-md text-center"
          >
            <div className="flex justify-center mb-4">
              <div className="p-4 bg-indigo-100 dark:bg-indigo-900/30 rounded-full">
                <Zap className="w-8 h-8 text-indigo-600 dark:text-indigo-400" />
              </div>
            </div>
            <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">Select a Skill Category</h3>
            <p className="text-gray-600 dark:text-gray-400 max-w-md mx-auto">
              Click on any category above to explore my skills and expertise in that area.
              Each category includes detailed proficiency levels and descriptions.
            </p>
          </motion.div>
        )}
      </AnimatePresence>
      
      {/* Skill Level Legend */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.5 }}
        viewport={{ once: true }}
        className="bg-white dark:bg-gray-800 rounded-xl p-4 shadow-md"
      >
        <h4 className="text-sm font-semibold uppercase tracking-wide text-gray-500 dark:text-gray-400 mb-3">Skill Level Legend</h4>
        <div className="flex flex-wrap gap-3">
          {[
            { label: 'Beginner', stars: 1, color: 'bg-yellow-400' },
            { label: 'Basic', stars: 2, color: 'bg-yellow-500' },
            { label: 'Intermediate', stars: 3, color: 'bg-green-500' },
            { label: 'Advanced', stars: 4, color: 'bg-blue-500' },
            { label: 'Expert', stars: 5, color: 'bg-indigo-600' },
          ].map((level) => (
            <div key={level.label} className="flex items-center space-x-2">
              <div className={`w-3 h-3 rounded-full ${level.color}`}></div>
              <div className="flex">
                {Array.from({ length: level.stars }).map((_, i) => (
                  <Star key={i} className="w-3 h-3 text-yellow-500 fill-yellow-500" />
                ))}
                {Array.from({ length: 5 - level.stars }).map((_, i) => (
                  <Star key={i} className="w-3 h-3 text-gray-300 dark:text-gray-600" />
                ))}
              </div>
              <span className="text-xs text-gray-600 dark:text-gray-400">{level.label}</span>
            </div>
          ))}
        </div>
      </motion.div>
    </MotionWrapper>
  );
} 