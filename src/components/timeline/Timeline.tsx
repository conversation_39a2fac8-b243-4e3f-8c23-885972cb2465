'use client';

import { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Briefcase, ChevronLeft, ChevronRight, Building, Award, MapPin, Calendar, ArrowRight, Users, Target, Clock, ExternalLink, Filter, Grid, History } from 'lucide-react';
import { useScrollReveal, useMagneticEffect, useStaggerAnimation } from '../animations/GSAPAnimations';
import { RippleButton } from '../animations/MicroInteractions';
import Link from 'next/link';

// Timeline component for the portfolio
export default function Timeline() {
  const [activeIndex, setActiveIndex] = useState(0);
  const [viewMode, setViewMode] = useState<'timeline' | 'cards' | '3d'>('timeline');
  const [filterCompany, setFilterCompany] = useState<string | null>(null);
  const [isExpanded, setIsExpanded] = useState(false);
  
  // Professional experience data from resume
  const timelineData = [
    {
      id: 'zawaya-pm',
      year: 'Jan 2024 – present',
      title: 'Product Manager',
      company: 'Zawaya',
      description: 'Led the creation of internal Flutter delivery app, developed Shopify website, and implemented ERP features saving $100K annually.',
      achievements: [
        'Increased customer orders by 25% through new Shopify website',
        'Built delivery system reducing delivery time and costs by 30%',
        'Implemented ERP features saving $100K annually',
        'Enhanced systems improving performance by 10%',
        'Achieved 15% increase in user engagement through A/B testing',
        'Integrated payment methods increasing checkout completion by 10%'
      ],
      color: 'blue',
      location: 'Cairo, Egypt',
      keySkills: ['Flutter', 'Shopify', 'ERP Integration', 'Product Strategy']
    },
    {
      id: 'zawaya-scm',
      year: 'Oct 2022 – Jan 2024',
      title: 'Supply chain Manager',
      company: 'Zawaya',
      description: 'Streamlined supply chain operations and implemented data-driven forecasting techniques.',
      achievements: [
        'Reduced lead times by 15% through effective vendor management',
        'Improved inventory turnover rates by 20%',
        'Established KPIs for supply chain efficiency',
        'Trained and mentored junior team members',
        'Enhanced visibility and accountability across teams'
      ],
      color: 'green',
      location: 'Cairo, Egypt',
      keySkills: ['Supply Chain', 'Data Forecasting', 'Vendor Management', 'KPIs']
    },
    {
      id: 'capiter-control',
      year: 'Jan 2022 – Sep 2022',
      title: 'Control Tower Lead',
      company: 'Capiter',
      description: 'Orchestrated project plans and implemented automation systems optimizing team efficiency.',
      achievements: [
        '15% surge in on-time project completions',
        '30% increase in overall productivity through automation',
        '15% enhancement in operational processes',
        '25% improvement in data visualization',
        '20% increase in cross-functional efficiency'
      ],
      color: 'purple',
      location: 'Cairo, Egypt',
      keySkills: ['Project Management', 'Automation', 'Data Analysis', 'Process Improvement']
    },
    {
      id: 'capiter-ops',
      year: 'Sep 2021 – Dec 2021',
      title: 'Live Operations Supervisor',
      company: 'Capiter',
      description: 'Streamlined meeting communication using agile methodology and managed team of 20+ for efficient collaboration.',
      achievements: [
        '40% reduction in meeting duration',
        '25% increase in action item completion rate',
        'Verified 70% of daily orders saving over 20 million monthly',
        '30% reduction in manual entry mistakes'
      ],
      color: 'orange',
      location: 'Cairo, Egypt',
      keySkills: ['Agile Methodology', 'Team Management', 'Process Optimization', 'Quality Control']
    },
    {
      id: 'misr-pharmacies',
      year: 'May 2021 – Sep 2021',
      title: 'Courier Management Senior Supervisor',
      company: 'Misr Pharmacies',
      description: 'Led team performance optimization and onboarding for new employees, achieving significant delivery improvements.',
      achievements: [
        '17% increase in overall team effectiveness',
        '30% improvement in task efficiency',
        '30% reduction in delivery time within three months',
        '15% improvement in task execution'
      ],
      color: 'teal',
      location: 'Giza, Egypt',
      keySkills: ['Team Leadership', 'Performance Optimization', 'Training', 'Delivery Management']
    },
    {
      id: 'bites',
      year: 'Aug 2020 – Mar 2021',
      title: 'Dispatcher (Operations) Team Leader',
      company: 'Bites',
      description: 'Led dispatching operations, resolved order issues, and implemented fraud prevention strategies.',
      achievements: [
        'Increased order volume and reduced delivery times',
        'Enhanced efficiency and cut labor expenses',
        'Implemented fraud prevention strategies',
        'Led daily meetings with multiple teams'
      ],
      color: 'red',
      location: 'Giza, Egypt',
      keySkills: ['Operations Management', 'Fraud Prevention', 'Team Leadership', 'Process Improvement']
    },
    {
      id: 'swvl-compliance',
      year: 'Aug 2019 – Jul 2020',
      title: 'Global Compliance Analyst Lead',
      company: 'SWVL',
      description: 'Conducted compliance audits across multiple countries and developed action plans for operational improvements.',
      achievements: [
        'Conducted compliance audits in Egypt, Pakistan, and Kenya',
        'Saved 25-28% from output through compliance improvements',
        'Developed action plans for operational improvements',
        'Assisted in fraud investigations and risk identification'
      ],
      color: 'indigo',
      location: 'Cairo, Egypt',
      keySkills: ['Compliance Auditing', 'Risk Management', 'International Operations', 'Fraud Investigation']
    },
    {
      id: 'swvl-team-leader',
      year: 'Oct 2018 – Aug 2019',
      title: 'Captain Help Team Leader',
      company: 'SWVL',
      description: 'Led a team of 20 members while providing exceptional customer service and implementing new working processes.',
      achievements: [
        'Led team of 20 members with exceptional customer service',
        'Implemented new working processes for continued improvements',
        'Trained team on operating procedures and company services',
        'Coached team members in customer service techniques'
      ],
      color: 'pink',
      location: 'Cairo, Egypt',
      keySkills: ['Team Leadership', 'Customer Service', 'Process Implementation', 'Training & Development']
    },
    {
      id: 'swvl-quality',
      year: 'Jul 2018 – Oct 2018',
      title: 'Fleet Quality Specialist',
      company: 'SWVL',
      description: 'Worked with quality team to resolve rides problems and improve operations while providing exceptional customer service.',
      achievements: [
        'Handled 500 rides per 8 hours',
        'Increased customer satisfaction by resolving ride issues',
        'Shortlisted for quality assurance role based on performance'
      ],
      color: 'cyan',
      location: 'Cairo, Egypt',
      keySkills: ['Quality Assurance', 'Customer Service', 'Operations Improvement', 'Problem Resolution']
    },
    {
      id: 'otlob',
      year: 'Apr 2017 – Jul 2018',
      title: 'Senior Customer Service Representative',
      company: 'Otlob (Talabat/Delivery Hero)',
      description: 'Performed various supportive functions and demonstrated exceptional attendance and readiness standards.',
      achievements: [
        'Maintained flawless attendance record',
        'Supported back-end team and tier 2 team',
        'Got opportunity to lead team',
        'Set standard for team performance'
      ],
      color: 'yellow',
      location: 'Cairo, Egypt',
      keySkills: ['Customer Service', 'Team Support', 'Leadership Development', 'Performance Standards']
    }
  ];
  
  // Filter timeline data if a company filter is applied
  const filteredTimelineData = filterCompany 
    ? timelineData.filter(item => item.company === filterCompany)
    : timelineData;

  // Get the active timeline item
  const activeItem = filteredTimelineData[activeIndex];
  
  // Navigation functions
  const goToPrevious = () => {
    setActiveIndex((prev) => (prev === 0 ? filteredTimelineData.length - 1 : prev - 1));
  };

  const goToNext = () => {
    setActiveIndex((prev) => (prev === filteredTimelineData.length - 1 ? 0 : prev + 1));
  };
  
  return (
    <div className="w-full">
      <h2 className="text-2xl font-bold mb-4">Professional Timeline</h2>
      
      {/* View mode selector */}
      <div className="flex space-x-4 mb-6">
        <button 
          onClick={() => setViewMode('timeline')} 
          className={`px-4 py-2 ${viewMode === 'timeline' ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}
        >
          <History className="h-4 w-4 inline mr-2" />
          Timeline
        </button>
        <button 
          onClick={() => setViewMode('cards')} 
          className={`px-4 py-2 ${viewMode === 'cards' ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}
        >
          <Grid className="h-4 w-4 inline mr-2" />
          Cards
        </button>
      </div>
      
      {/* Timeline view */}
      {viewMode === 'timeline' && (
        <div className="p-6 bg-white rounded-lg shadow">
          <h3 className="text-xl font-bold">{activeItem.title}</h3>
          <p className="text-gray-600">{activeItem.company} • {activeItem.year}</p>
          <p className="mt-4">{activeItem.description}</p>
          
          <div className="mt-4">
            <h4 className="font-semibold">Achievements</h4>
            <ul className="list-disc pl-5 mt-2">
              {activeItem.achievements.map((achievement, idx) => (
                <li key={idx}>{achievement}</li>
              ))}
            </ul>
          </div>
          
          <div className="flex justify-between mt-6">
            <button onClick={goToPrevious} className="px-3 py-1 bg-gray-200 rounded">
              <ChevronLeft className="h-4 w-4 inline" /> Previous
            </button>
            <button onClick={goToNext} className="px-3 py-1 bg-gray-200 rounded">
              Next <ChevronRight className="h-4 w-4 inline" />
            </button>
          </div>
        </div>
      )}
      
      {/* Cards view */}
      {viewMode === 'cards' && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {filteredTimelineData.map((item, index) => (
            <div key={item.id} className="p-4 bg-white rounded-lg shadow">
              <h3 className="text-lg font-bold">{item.title}</h3>
              <p className="text-gray-600">{item.company} • {item.year}</p>
              <p className="mt-2">{item.description}</p>
            </div>
          ))}
        </div>
      )}
    </div>
  );
} 