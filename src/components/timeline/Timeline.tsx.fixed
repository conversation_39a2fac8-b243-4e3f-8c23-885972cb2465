'use client';

import { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Briefcase, ChevronLeft, ChevronRight, Building, Award, MapPin, Calendar, ArrowRight, Users, Target, Clock, ExternalLink, Filter, Grid, History } from 'lucide-react';
import { useScrollReveal, useMagneticEffect, useStaggerAnimation } from '../animations/GSAPAnimations';
import { RippleButton } from '../animations/MicroInteractions';
import Link from 'next/link';

// Timeline component for the portfolio
export default function Timeline() {
  const [activeIndex, setActiveIndex] = useState(0);
  const [viewMode, setViewMode] = useState<'timeline' | 'cards' | '3d'>('timeline');
  const [filterCompany, setFilterCompany] = useState<string | null>(null);
  const [isExpanded, setIsExpanded] = useState(false);
  
  // Simple data for testing
  const timelineData = [
    {
      id: 'job1',
      year: '2023-2024',
      title: 'Product Manager',
      company: 'Example Company',
      description: 'Led product development initiatives.',
      achievements: ['Achievement 1', 'Achievement 2'],
      color: 'blue',
      location: 'Remote',
      keySkills: ['Product Strategy', 'Agile']
    },
    {
      id: 'job2',
      year: '2021-2023',
      title: 'Developer',
      company: 'Tech Firm',
      description: 'Developed web applications.',
      achievements: ['Achievement 1', 'Achievement 2'],
      color: 'green',
      location: 'New York',
      keySkills: ['React', 'Next.js']
    }
  ];
  
  // Filter timeline data if a company filter is applied
  const filteredTimelineData = filterCompany 
    ? timelineData.filter(item => item.company === filterCompany)
    : timelineData;

  // Get the active timeline item
  const activeItem = filteredTimelineData[activeIndex];
  
  // Navigation functions
  const goToPrevious = () => {
    setActiveIndex((prev) => (prev === 0 ? filteredTimelineData.length - 1 : prev - 1));
  };

  const goToNext = () => {
    setActiveIndex((prev) => (prev === filteredTimelineData.length - 1 ? 0 : prev + 1));
  };
  
  return (
    <div className="w-full">
      <h2 className="text-2xl font-bold mb-4">Professional Timeline</h2>
      
      {/* View mode selector */}
      <div className="flex space-x-4 mb-6">
        <button 
          onClick={() => setViewMode('timeline')} 
          className={`px-4 py-2 ${viewMode === 'timeline' ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}
        >
          <History className="h-4 w-4 inline mr-2" />
          Timeline
        </button>
        <button 
          onClick={() => setViewMode('cards')} 
          className={`px-4 py-2 ${viewMode === 'cards' ? 'bg-blue-500 text-white' : 'bg-gray-200'}`}
        >
          <Grid className="h-4 w-4 inline mr-2" />
          Cards
        </button>
      </div>
      
      {/* Timeline view */}
      {viewMode === 'timeline' && (
        <div className="p-6 bg-white rounded-lg shadow">
          <h3 className="text-xl font-bold">{activeItem.title}</h3>
          <p className="text-gray-600">{activeItem.company} • {activeItem.year}</p>
          <p className="mt-4">{activeItem.description}</p>
          
          <div className="mt-4">
            <h4 className="font-semibold">Achievements</h4>
            <ul className="list-disc pl-5 mt-2">
              {activeItem.achievements.map((achievement, idx) => (
                <li key={idx}>{achievement}</li>
              ))}
            </ul>
          </div>
          
          <div className="flex justify-between mt-6">
            <button onClick={goToPrevious} className="px-3 py-1 bg-gray-200 rounded">
              <ChevronLeft className="h-4 w-4 inline" /> Previous
            </button>
            <button onClick={goToNext} className="px-3 py-1 bg-gray-200 rounded">
              Next <ChevronRight className="h-4 w-4 inline" />
            </button>
          </div>
        </div>
      )}
      
      {/* Cards view */}
      {viewMode === 'cards' && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {filteredTimelineData.map((item, index) => (
            <div key={item.id} className="p-4 bg-white rounded-lg shadow">
              <h3 className="text-lg font-bold">{item.title}</h3>
              <p className="text-gray-600">{item.company} • {item.year}</p>
              <p className="mt-2">{item.description}</p>
            </div>
          ))}
        </div>
      )}
    </div>
  );
} 