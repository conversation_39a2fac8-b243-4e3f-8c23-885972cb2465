'use client';

import { useState, useRef } from 'react';
import { motion } from 'framer-motion';
import { Briefcase, Building, Calendar, MapPin, ChevronLeft, ChevronRight, Award, Users, TrendingUp } from 'lucide-react';

interface TimelineItem {
  id: string;
  year: string;
  title: string;
  company: string;
  description: string;
  achievements: string[];
  color: string;
  location?: string;
  teamSize?: number;
  keySkills?: string[];
}

const timelineData: TimelineItem[] = [
  {
    id: 'zawaya-pm',
    year: '2024',
    title: 'Product Manager',
    company: 'Zawaya',
    description: 'Led product development initiatives, focusing on e-commerce solutions and internal tools.',
    achievements: [
      'Supported back-end team and tier 2 operations',
      'Maintained a flawless attendance record',
      'Set standards for team performance'
    ],
    color: 'indigo',
    location: 'Cairo, Egypt',
    teamSize: 8,
    keySkills: ['Product Strategy', 'Team Leadership', 'E-commerce', 'Data Analysis']
  },
  {
    id: 'zawaya-scm',
    year: '2022-2024',
    title: 'Supply Chain Manager',
    company: '<PERSON>awaya',
    description: 'Managed end-to-end supply chain operations, optimizing procurement and logistics.',
    achievements: [
      'Reduced lead times by 15% through effective vendor management',
      'Improved inventory turnover rates by 20% using data-driven forecasting',
      'Established KPIs for tracking supply chain efficiency'
    ],
    color: 'blue',
    location: 'Cairo, Egypt',
    teamSize: 12,
    keySkills: ['Supply Chain', 'Logistics', 'Data Forecasting', 'Vendor Management']
  },
  {
    id: 'capiter',
    year: '2022',
    title: 'Control Tower Lead',
    company: 'Capiter',
    description: 'Orchestrated project plans and managed team operations for maximum efficiency.',
    achievements: [
      'Implemented an automation system, increasing productivity by 30%',
      'Produced reports with a 25% improvement in data visualization',
      'Monitored operations, reducing potential impacts by 30%'
    ],
    color: 'purple',
    location: 'Cairo, Egypt',
    teamSize: 6,
    keySkills: ['Project Management', 'Automation', 'Data Visualization', 'Operations']
  }
];

export default function SimpleTimeline() {
  const [activeIndex, setActiveIndex] = useState(0);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const activeItem = timelineData[activeIndex];

  const scrollToCard = (index: number) => {
    setActiveIndex(index);
    if (scrollContainerRef.current) {
      const cardWidth = 400; // Width of each card + gap
      const scrollPosition = index * cardWidth;
      scrollContainerRef.current.scrollTo({
        left: scrollPosition,
        behavior: 'smooth'
      });
    }
  };

  const goToPrevious = () => {
    const newIndex = activeIndex === 0 ? timelineData.length - 1 : activeIndex - 1;
    scrollToCard(newIndex);
  };

  const goToNext = () => {
    const newIndex = activeIndex === timelineData.length - 1 ? 0 : activeIndex + 1;
    scrollToCard(newIndex);
  };

  return (
    <div className="w-full space-y-8">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent mb-2">
          Professional Journey
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          Explore my career progression through interactive timeline cards
        </p>
      </div>

      {/* Navigation Controls */}
      <div className="flex justify-between items-center">
        <button
          onClick={goToPrevious}
          className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-lg hover:from-purple-700 hover:to-blue-700 transition-all shadow-lg disabled:opacity-50"
        >
          <ChevronLeft className="h-5 w-5" />
          Previous
        </button>

        <div className="text-center">
          <p className="text-lg font-semibold text-gray-700 dark:text-gray-300">
            {activeIndex + 1} of {timelineData.length}
          </p>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {activeItem.year} • {activeItem.company}
          </p>
        </div>

        <button
          onClick={goToNext}
          className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-lg hover:from-purple-700 hover:to-blue-700 transition-all shadow-lg disabled:opacity-50"
        >
          Next
          <ChevronRight className="h-5 w-5" />
        </button>
      </div>

      {/* Horizontal Scrolling Timeline Cards */}
      <div className="relative">
        {/* Scroll hint */}
        <div className="flex justify-center mb-4">
          <div className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
            <span>Scroll horizontally or use navigation buttons</span>
            <motion.div
              animate={{ x: [0, 10, 0] }}
              transition={{ duration: 2, repeat: Infinity }}
              className="flex"
            >
              →
            </motion.div>
          </div>
        </div>

        <div
          ref={scrollContainerRef}
          className="overflow-x-auto scrollbar-hide pb-4"
          style={{ scrollBehavior: 'smooth' }}
        >
          <div className="flex gap-6 w-max px-4">
            {timelineData.map((item, index) => {
              const isActive = index === activeIndex;
              const distance = Math.abs(index - activeIndex);
              const scale = isActive ? 1.05 : Math.max(0.95, 1 - distance * 0.02);
              const opacity = isActive ? 1 : Math.max(0.8, 1 - distance * 0.1);

              return (
                <motion.div
                  key={item.id}
                  onClick={() => scrollToCard(index)}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  animate={{
                    scale: scale,
                    opacity: opacity,
                    y: isActive ? -10 : 0
                  }}
                  whileHover={{
                    y: isActive ? -15 : -5,
                    scale: scale * 1.02
                  }}
                  className={`flex-shrink-0 w-96 bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden cursor-pointer transition-all duration-300 ${
                    isActive ? 'ring-4 ring-purple-500/30 shadow-purple-500/20' : ''
                  }`}
                  style={{ minHeight: '500px' }}
                >
                  {/* Card header with gradient */}
                  <div className={`h-2 w-full bg-gradient-to-r from-${item.color}-500 to-${item.color}-600`}></div>

                  <div className="p-6 h-full flex flex-col">
                    {/* Header Section */}
                    <div className="flex justify-between items-start mb-6">
                      <div className={`p-3 rounded-xl bg-gradient-to-br from-${item.color}-100 to-${item.color}-200 dark:from-${item.color}-900/30 dark:to-${item.color}-800/30 text-${item.color}-600 dark:text-${item.color}-400`}>
                        <Briefcase className="h-6 w-6" />
                      </div>
                      <div className="flex items-center gap-2 px-3 py-1.5 bg-gradient-to-r from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 rounded-full">
                        <Calendar className="h-4 w-4 text-gray-500 dark:text-gray-400" />
                        <span className="text-sm font-semibold text-gray-700 dark:text-gray-300">{item.year}</span>
                      </div>
                    </div>

                    {/* Title and Company */}
                    <div className="mb-4">
                      <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2 leading-tight">{item.title}</h3>
                      <div className="flex items-center text-purple-600 dark:text-purple-400 mb-3">
                        <Building className="h-5 w-5 mr-2" />
                        <span className="font-semibold">{item.company}</span>
                      </div>

                      {item.location && (
                        <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                          <MapPin className="h-4 w-4 mr-2" />
                          <span>{item.location}</span>
                        </div>
                      )}
                    </div>

                    {/* Stats */}
                    {item.teamSize && (
                      <div className="flex items-center mb-4 text-sm">
                        <Users className="h-4 w-4 mr-2 text-gray-500" />
                        <span className="text-gray-600 dark:text-gray-400">Team Size: {item.teamSize} members</span>
                      </div>
                    )}

                    {/* Description */}
                    <div className="flex-1 mb-4">
                      <p className="text-sm text-gray-600 dark:text-gray-300 leading-relaxed">
                        {item.description}
                      </p>
                    </div>

                    {/* Achievements */}
                    <div className="mb-4">
                      <h4 className="text-sm font-bold text-gray-900 dark:text-white flex items-center mb-3">
                        <Award className="h-4 w-4 mr-2 text-amber-500" />
                        Key Achievements
                      </h4>
                      <div className="space-y-2 max-h-32 overflow-y-auto">
                        {item.achievements.slice(0, isActive ? 3 : 2).map((achievement, idx) => (
                          <div key={idx} className="flex items-start text-sm bg-gray-50 dark:bg-gray-700/30 p-2 rounded-lg">
                            <div className="h-2 w-2 rounded-full bg-green-500 mr-2 mt-2 flex-shrink-0"></div>
                            <span className="text-gray-700 dark:text-gray-300">{achievement}</span>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Skills Tags */}
                    {item.keySkills && (
                      <div className="mt-auto">
                        <div className="flex flex-wrap gap-1">
                          {item.keySkills.slice(0, isActive ? 4 : 3).map((skill, idx) => (
                            <span
                              key={idx}
                              className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-700 dark:bg-purple-900/30 dark:text-purple-300"
                            >
                              {skill}
                            </span>
                          ))}
                          {item.keySkills.length > (isActive ? 4 : 3) && (
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-300">
                              +{item.keySkills.length - (isActive ? 4 : 3)}
                            </span>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                </motion.div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Timeline Dots Navigation */}
      <div className="flex justify-center">
        <div className="flex items-center gap-4">
          {timelineData.map((item, index) => (
            <button
              key={item.id}
              onClick={() => scrollToCard(index)}
              className="relative flex flex-col items-center group"
            >
              {/* Dot */}
              <div className={`h-4 w-4 rounded-full border-2 transition-all duration-300 ${
                index === activeIndex
                  ? `border-white bg-${item.color}-500 scale-125 shadow-lg shadow-${item.color}-500/50`
                  : 'border-gray-300 bg-gray-200 hover:bg-gray-300 group-hover:scale-110'
              }`}></div>

              {/* Year Label */}
              <div className={`mt-2 text-xs font-medium transition-all duration-300 ${
                index === activeIndex
                  ? 'text-purple-600 dark:text-purple-400'
                  : 'text-gray-500 dark:text-gray-400 group-hover:text-gray-700 dark:group-hover:text-gray-300'
              }`}>
                {item.year}
              </div>
            </button>
          ))}
        </div>
      </div>
    </div>
  );
}
