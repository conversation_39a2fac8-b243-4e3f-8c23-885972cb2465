'use client';

import { useState, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronLeft, ChevronRight, Quote, LinkedinIcon, Award, ThumbsUp } from 'lucide-react';

interface Testimonial {
  id: string;
  quote: string;
  name: string;
  title: string;
  company: string;
  relationship: string;
  avatar?: string;
  linkedin?: string;
}

interface Endorsement {
  id: string;
  skill: string;
  count: number;
  names: string[];
}

const testimonials: Testimonial[] = [
  {
    id: 'ahmed',
    quote: "<PERSON><PERSON><PERSON> consistently demonstrates exceptional leadership skills. His ability to understand complex problems and develop innovative solutions made him an invaluable asset to our team. His strategic thinking and meticulous attention to detail have significantly improved our operational efficiency.",
    name: "<PERSON>",
    title: "COO",
    company: "<PERSON>awaya",
    relationship: "Direct Manager",
    avatar: "/assets/images/ahmed.jpg",
    linkedin: "https://linkedin.com/in/ahmed-mahmoud"
  },
  {
    id: 'sara',
    quote: "Working with <PERSON><PERSON><PERSON> was a transformative experience for our supply chain operations. His strategic vision and practical implementation skills helped us reduce costs while improving service levels. His collaborative approach ensures that everyone feels valued and part of the solution.",
    name: "<PERSON>",
    title: "Supply Chain Director",
    company: "Capiter",
    relationship: "Col<PERSON>",
    avatar: "/assets/images/sara.jpg",
    linkedin: "https://linkedin.com/in/sara-hassan"
  },
  {
    id: 'omar',
    quote: "Magdi brings a unique blend of technical knowledge and business acumen to product management. He truly understands customer needs and translates them into practical, innovative solutions. His project management skills ensured we delivered on time and within budget.",
    name: "Omar Khalid",
    title: "CTO",
    company: "Zawaya",
    relationship: "Project Partner",
    avatar: "/assets/images/omar.jpg",
    linkedin: "https://linkedin.com/in/omar-khalid"
  },
  {
    id: 'laila',
    quote: "I had the pleasure of working with Magdi on several cross-functional initiatives. His ability to bridge the gap between technical and business teams is remarkable. He's not only knowledgeable about product development but also understands the market dynamics that drive success.",
    name: "Laila Adel",
    title: "Marketing Manager",
    company: "SWVL",
    relationship: "Collaborator",
    avatar: "/assets/images/laila.jpg",
    linkedin: "https://linkedin.com/in/laila-adel"
  }
];

const endorsements: Endorsement[] = [
  {
    id: 'product-management',
    skill: 'Product Management',
    count: 15,
    names: ['Ahmed Mahmoud', 'Omar Khalid', 'Laila Adel', '+12 others']
  },
  {
    id: 'supply-chain',
    skill: 'Supply Chain Management',
    count: 12,
    names: ['Sara Hassan', 'Ahmed Mahmoud', '+10 others']
  },
  {
    id: 'leadership',
    skill: 'Leadership',
    count: 18,
    names: ['Omar Khalid', 'Laila Adel', 'Sara Hassan', '+15 others']
  },
  {
    id: 'problem-solving',
    skill: 'Problem Solving',
    count: 14,
    names: ['Ahmed Mahmoud', 'Omar Khalid', '+12 others']
  },
  {
    id: 'analytics',
    skill: 'Data Analytics',
    count: 9,
    names: ['Omar Khalid', 'Sara Hassan', '+7 others']
  }
];

export default function SocialProof() {
  const [currentTestimonial, setCurrentTestimonial] = useState(0);
  const [direction, setDirection] = useState(0);
  const testimonialsRef = useRef<HTMLDivElement>(null);
  
  const previousTestimonial = () => {
    setDirection(-1);
    setCurrentTestimonial((prev) => (prev === 0 ? testimonials.length - 1 : prev - 1));
  };
  
  const nextTestimonial = () => {
    setDirection(1);
    setCurrentTestimonial((prev) => (prev === testimonials.length - 1 ? 0 : prev + 1));
  };
  
  const variants = {
    enter: (direction: number) => ({
      x: direction > 0 ? 300 : -300,
      opacity: 0
    }),
    center: {
      x: 0,
      opacity: 1
    },
    exit: (direction: number) => ({
      x: direction < 0 ? 300 : -300,
      opacity: 0
    })
  };
  
  return (
    <div className="w-full bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
      <div className="p-6">
        <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-2">Social Proof</h2>
        <p className="text-gray-600 dark:text-gray-400 mb-8">
          What colleagues and collaborators say about working with me.
        </p>
        
        {/* Testimonials Carousel */}
        <div className="relative mb-12" ref={testimonialsRef}>
          <div className="relative h-[350px] overflow-hidden bg-indigo-50 dark:bg-indigo-900/20 rounded-lg">
            <AnimatePresence initial={false} custom={direction} mode="wait">
              <motion.div
                key={currentTestimonial}
                custom={direction}
                variants={variants}
                initial="enter"
                animate="center"
                exit="exit"
                transition={{ type: 'tween', duration: 0.5 }}
                className="absolute w-full h-full p-8 flex flex-col justify-between"
              >
                <div className="flex-1">
                  <Quote className="h-10 w-10 text-indigo-300 dark:text-indigo-700 mb-4" />
                  <p className="text-lg text-gray-700 dark:text-gray-200 italic leading-relaxed">
                    "{testimonials[currentTestimonial].quote}"
                  </p>
                </div>
                
                <div className="mt-6 flex items-center">
                  {testimonials[currentTestimonial].avatar ? (
                    <img 
                      src={testimonials[currentTestimonial].avatar} 
                      alt={testimonials[currentTestimonial].name}
                      className="w-12 h-12 rounded-full object-cover mr-4"
                    />
                  ) : (
                    <div className="w-12 h-12 rounded-full bg-indigo-200 dark:bg-indigo-800 flex items-center justify-center mr-4">
                      <span className="text-indigo-600 dark:text-indigo-300 font-medium text-lg">
                        {testimonials[currentTestimonial].name.charAt(0)}
                      </span>
                    </div>
                  )}
                  
                  <div>
                    <div className="flex items-center">
                      <h3 className="font-medium text-gray-900 dark:text-white">
                        {testimonials[currentTestimonial].name}
                      </h3>
                      {testimonials[currentTestimonial].linkedin && (
                        <a 
                          href={testimonials[currentTestimonial].linkedin} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="ml-2 text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
                          aria-label={`${testimonials[currentTestimonial].name}'s LinkedIn profile`}
                        >
                          <LinkedinIcon className="h-4 w-4" />
                        </a>
                      )}
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {testimonials[currentTestimonial].title}, {testimonials[currentTestimonial].company}
                    </p>
                    <p className="text-xs text-indigo-600 dark:text-indigo-400 mt-1">
                      {testimonials[currentTestimonial].relationship}
                    </p>
                  </div>
                </div>
              </motion.div>
            </AnimatePresence>
          </div>
          
          {/* Navigation Controls */}
          <div className="absolute bottom-4 right-4 flex space-x-2">
            <button
              onClick={previousTestimonial}
              className="p-2 rounded-full bg-white/80 dark:bg-gray-800/80 text-gray-700 dark:text-gray-300 hover:bg-indigo-100 dark:hover:bg-indigo-900 backdrop-blur-sm"
              aria-label="Previous testimonial"
            >
              <ChevronLeft className="h-5 w-5" />
            </button>
            <button
              onClick={nextTestimonial}
              className="p-2 rounded-full bg-white/80 dark:bg-gray-800/80 text-gray-700 dark:text-gray-300 hover:bg-indigo-100 dark:hover:bg-indigo-900 backdrop-blur-sm"
              aria-label="Next testimonial"
            >
              <ChevronRight className="h-5 w-5" />
            </button>
          </div>
          
          {/* Dots Indicator */}
          <div className="absolute -bottom-6 left-0 right-0 flex justify-center space-x-2">
            {testimonials.map((_, index) => (
              <button
                key={index}
                onClick={() => {
                  setDirection(index > currentTestimonial ? 1 : -1);
                  setCurrentTestimonial(index);
                }}
                className={`w-2 h-2 rounded-full transition-all ${
                  index === currentTestimonial
                    ? 'bg-indigo-600 dark:bg-indigo-400 w-4'
                    : 'bg-gray-300 dark:bg-gray-600'
                }`}
                aria-label={`Go to testimonial ${index + 1}`}
              />
            ))}
          </div>
        </div>
        
        {/* Skills Endorsements */}
        <div className="mt-12">
          <div className="flex items-center mb-6">
            <Award className="h-5 w-5 text-indigo-600 dark:text-indigo-400 mr-2" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Skills Endorsements</h3>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {endorsements.map((endorsement) => (
              <motion.div
                key={endorsement.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3 }}
                className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 hover:shadow-md transition-shadow"
              >
                <div className="flex justify-between items-start mb-3">
                  <h4 className="font-medium text-gray-900 dark:text-white">{endorsement.skill}</h4>
                  <div className="flex items-center text-indigo-600 dark:text-indigo-400">
                    <ThumbsUp className="h-4 w-4 mr-1" />
                    <span className="font-semibold">{endorsement.count}</span>
                  </div>
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Endorsed by: {endorsement.names.join(', ')}
                </p>
              </motion.div>
            ))}
          </div>
        </div>
        
        {/* Other Recognitions */}
        <div className="mt-10 bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
          <div className="flex items-center mb-4">
            <Award className="h-5 w-5 text-indigo-600 dark:text-indigo-400 mr-2" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Additional Recognition</h3>
          </div>
          
          <ul className="space-y-3">
            <li className="flex items-start">
              <div className="mr-3 bg-indigo-100 dark:bg-indigo-900/50 p-1 rounded-full">
                <Award className="h-4 w-4 text-indigo-600 dark:text-indigo-400" />
              </div>
              <div>
                <p className="text-gray-700 dark:text-gray-300">
                  <span className="font-medium">Top Performer Award</span> - Zawaya (2023)
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Recognized for exceptional contribution to product development initiatives.
                </p>
              </div>
            </li>
            <li className="flex items-start">
              <div className="mr-3 bg-indigo-100 dark:bg-indigo-900/50 p-1 rounded-full">
                <Award className="h-4 w-4 text-indigo-600 dark:text-indigo-400" />
              </div>
              <div>
                <p className="text-gray-700 dark:text-gray-300">
                  <span className="font-medium">Innovation Champion</span> - Capiter (2022)
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Awarded for implementing automation solutions that increased productivity by 30%.
                </p>
              </div>
            </li>
            <li className="flex items-start">
              <div className="mr-3 bg-indigo-100 dark:bg-indigo-900/50 p-1 rounded-full">
                <Award className="h-4 w-4 text-indigo-600 dark:text-indigo-400" />
              </div>
              <div>
                <p className="text-gray-700 dark:text-gray-300">
                  <span className="font-medium">Excellence in Leadership</span> - SWVL (2019)
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Recognized for outstanding team management and mentorship.
                </p>
              </div>
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
} 