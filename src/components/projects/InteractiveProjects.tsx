'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, ExternalLink, Github, ChevronRight, Award, Code, Activity, PlayCircle } from 'lucide-react';

interface ProjectTag {
  id: string;
  name: string;
}

interface ProjectDetail {
  id: string;
  title: string;
  description: string;
  tags: string[];
  image: string;
  achievements: string[];
  technologies: string[];
  challenges: string[];
  solutions: string[];
  results: string[];
  liveUrl?: string;
  githubUrl?: string;
  videoUrl?: string;
}

const projectTags: ProjectTag[] = [
  { id: 'all', name: 'All Projects' },
  { id: 'product', name: 'Product Management' },
  { id: 'ecommerce', name: 'E-Commerce' },
  { id: 'mobile', name: 'Mobile Apps' },
  { id: 'web', name: 'Web Applications' },
  { id: 'supply', name: 'Supply Chain' }
];

const projects: ProjectDetail[] = [
  {
    id: 'shopify-website',
    title: 'Zawaya E-Commerce Platform',
    description: 'Led the development of a comprehensive Shopify website that significantly improved the customer shopping experience and increased sales.',
    tags: ['ecommerce', 'web', 'product'],
    image: '/assets/images/zawaya-ecommerce.jpg',
    achievements: [
      'Increased online orders by 25%',
      'Improved conversion rate by 15%',
      'Reduced cart abandonment by 22%',
      'Enhanced mobile experience, resulting in 30% more mobile sales'
    ],
    technologies: [
      'Shopify', 'Liquid Templates', 'JavaScript', 'CSS/SCSS', 'Shopify Apps Integration', 
      'Payment Gateways', 'Google Analytics', 'Facebook Pixel'
    ],
    challenges: [
      'Migrating from a legacy platform with minimal disruption',
      'Ensuring seamless inventory synchronization between online and physical stores',
      'Creating a unique brand experience while utilizing Shopify\'s platform',
      'Optimizing the mobile experience for a primarily mobile customer base'
    ],
    solutions: [
      'Developed a comprehensive migration plan with phased implementation',
      'Integrated ERP system with Shopify through custom API development',
      'Customized a premium theme with brand-specific elements and UX improvements',
      'Implemented responsive design principles with mobile-first approach'
    ],
    results: [
      'Successfully launched the platform two weeks ahead of schedule',
      'Received positive customer feedback on improved usability',
      'Increased average order value by 18%',
      'Expanded market reach with international shipping capabilities'
    ],
    liveUrl: 'https://example-zawaya.com'
  },
  {
    id: 'delivery-app',
    title: 'Internal Delivery Management App',
    description: 'Designed and oversaw the development of a Flutter-based delivery management application that revolutionized our logistics operations.',
    tags: ['mobile', 'product', 'supply'],
    image: '/assets/images/delivery-app.jpg',
    achievements: [
      'Reduced delivery times by 28%',
      'Increased deliveries per agent by 35%',
      'Saved 22% in fuel costs',
      'Improved on-time delivery rate to 94%'
    ],
    technologies: [
      'Flutter', 'Dart', 'Firebase', 'Google Maps API', 'Real-time Database', 
      'Push Notifications', 'GPS Tracking', 'Analytics Dashboard'
    ],
    challenges: [
      'Creating a solution that works reliably in areas with poor connectivity',
      'Ensuring the app is intuitive for delivery agents with varying technical skills',
      'Developing accurate route optimization algorithms',
      'Building real-time tracking with minimal battery consumption'
    ],
    solutions: [
      'Implemented offline-first architecture with data synchronization',
      'Conducted extensive user testing with actual delivery agents to refine UX',
      'Utilized Google Maps API with custom algorithms for local route optimization',
      'Balanced tracking frequency with battery optimization techniques'
    ],
    results: [
      'App adopted by 100% of delivery agents within the first month',
      'Significantly improved customer satisfaction with real-time tracking',
      'Management gained valuable insights through comprehensive analytics',
      'Reduced customer complaints by 45%'
    ],
    videoUrl: 'https://example.com/delivery-app-demo'
  },
  {
    id: 'erp-integration',
    title: 'ERP Features Implementation',
    description: 'Led the development and integration of key ERP features directly into our core system, eliminating the need for costly third-party solutions.',
    tags: ['web', 'product', 'supply'],
    image: '/assets/images/erp-system.jpg',
    achievements: [
      'Saved $100K annually in third-party ERP costs',
      'Reduced manual data entry by 75%',
      'Improved reporting speed by 60%',
      'Achieved 92% user adoption rate'
    ],
    technologies: [
      'Cloud Architecture', 'API Development', 'Database Design', 'Workflow Automation', 
      'Data Migration', 'SQL', 'UI/UX Design', 'Security Implementation'
    ],
    challenges: [
      'Integrating new features without disrupting existing workflows',
      'Ensuring data consistency across previously siloed systems',
      'Training staff on new processes and interfaces',
      'Managing complex data migration from legacy systems'
    ],
    solutions: [
      'Developed a modular architecture allowing phased implementation',
      'Created a centralized data repository with robust validation rules',
      'Designed intuitive interfaces that mimicked familiar workflow patterns',
      'Built custom migration tools with extensive validation and rollback capabilities'
    ],
    results: [
      'Successfully transitioned from third-party systems in under 6 months',
      'Improved data accuracy and eliminated redundancies',
      'Enhanced cross-departmental visibility and collaboration',
      'Created a platform for continuous process improvement'
    ],
    githubUrl: 'https://github.com/example/erp-core'
  },
  {
    id: 'crm-web',
    title: 'CRM Web Application',
    description: 'Created a tailored CRM solution for the sales and finance teams that streamlined customer management and improved collaboration.',
    tags: ['web', 'product'],
    image: '/assets/images/crm-webapp.jpg',
    achievements: [
      'Increased sales team productivity by 32%',
      'Reduced sales cycle length by 20%',
      'Improved lead conversion rate by 15%',
      'Enhanced customer data accuracy by 40%'
    ],
    technologies: [
      'React', 'Node.js', 'MongoDB', 'Express', 'RESTful APIs', 
      'Authentication & Authorization', 'Data Visualization', 'PDF Report Generation'
    ],
    challenges: [
      'Accommodating different workflows between sales and finance teams',
      'Ensuring robust data security while maintaining ease of access',
      'Creating an intuitive interface that minimizes training requirements',
      'Building comprehensive reporting without performance degradation'
    ],
    solutions: [
      'Implemented role-based access with customizable dashboards',
      'Developed a multi-layered security approach with encryption',
      'Utilized user-centered design principles with extensive stakeholder input',
      'Created a separate reporting service with optimized data queries'
    ],
    results: [
      'Significantly improved cross-department collaboration',
      'Enhanced data-driven decision making with real-time insights',
      'Reduced time spent on administrative tasks by 45%',
      'Created a central knowledge repository of customer interactions'
    ],
    liveUrl: 'https://crm-demo.example.com',
    githubUrl: 'https://github.com/example/crm-application'
  }
];

export default function InteractiveProjects() {
  const [selectedTag, setSelectedTag] = useState('all');
  const [selectedProject, setSelectedProject] = useState<ProjectDetail | null>(null);
  const [activeTab, setActiveTab] = useState('overview');
  
  const filteredProjects = selectedTag === 'all' 
    ? projects 
    : projects.filter(project => project.tags.includes(selectedTag));
  
  const closeProjectDetail = () => {
    setSelectedProject(null);
    setActiveTab('overview');
  };
  
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };
  
  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: { duration: 0.4 }
    }
  };
  
  const overlayVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1, transition: { duration: 0.3 } },
    exit: { opacity: 0, transition: { duration: 0.3 } }
  };
  
  const modalVariants = {
    hidden: { opacity: 0, scale: 0.8, y: 50 },
    visible: { 
      opacity: 1, 
      scale: 1, 
      y: 0,
      transition: { duration: 0.4, ease: 'easeOut' }
    },
    exit: { 
      opacity: 0, 
      scale: 0.8, 
      y: 50,
      transition: { duration: 0.3, ease: 'easeIn' }
    }
  };
  
  return (
    <div className="w-full">
      {/* Filter Tags */}
      <div className="mb-8 flex flex-wrap gap-2">
        {projectTags.map(tag => (
          <button
            key={tag.id}
            onClick={() => setSelectedTag(tag.id)}
            className={`px-4 py-2 rounded-full text-sm font-medium transition-all ${
              selectedTag === tag.id
                ? 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200'
                : 'bg-gray-100 text-gray-600 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
            }`}
          >
            {tag.name}
          </button>
        ))}
      </div>
      
      {/* Projects Grid */}
      <motion.div 
        className="grid grid-cols-1 md:grid-cols-2 gap-6"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {filteredProjects.map(project => (
          <motion.div
            key={project.id}
            variants={itemVariants}
            className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow cursor-pointer"
            onClick={() => setSelectedProject(project)}
          >
            <div className="h-48 overflow-hidden relative">
              <div className="absolute inset-0 bg-gradient-to-b from-transparent to-black/50 z-10"></div>
              {project.image ? (
                <img 
                  src={project.image} 
                  alt={project.title} 
                  className="w-full h-full object-cover transition-transform duration-500 hover:scale-105"
                />
              ) : (
                <div className="w-full h-full bg-gradient-to-r from-indigo-500 to-purple-600 flex items-center justify-center">
                  <Code className="text-white h-12 w-12 opacity-50" />
                </div>
              )}
              <div className="absolute bottom-4 left-4 right-4 z-20">
                <h3 className="text-xl font-semibold text-white">{project.title}</h3>
                <div className="flex flex-wrap mt-2 gap-1">
                  {project.tags.map(tag => (
                    <span 
                      key={`${project.id}-${tag}`} 
                      className="text-xs px-2 py-1 bg-black/30 backdrop-blur-sm text-white rounded"
                    >
                      {projectTags.find(t => t.id === tag)?.name || tag}
                    </span>
                  ))}
                </div>
              </div>
            </div>
            <div className="p-4">
              <p className="text-gray-600 dark:text-gray-300 line-clamp-2">{project.description}</p>
              <div className="mt-4 flex items-center text-indigo-600 dark:text-indigo-400 font-medium">
                <span>View details</span>
                <ChevronRight className="h-4 w-4 ml-1" />
              </div>
            </div>
          </motion.div>
        ))}
      </motion.div>
      
      {/* Project Detail Modal */}
      <AnimatePresence>
        {selectedProject && (
          <>
            <motion.div 
              className="fixed inset-0 bg-black/70 backdrop-blur-sm z-40"
              variants={overlayVariants}
              initial="hidden"
              animate="visible"
              exit="exit"
              onClick={closeProjectDetail}
            ></motion.div>
            
            <motion.div 
              className="fixed inset-0 z-50 flex items-center justify-center p-4"
              variants={overlayVariants}
              initial="hidden"
              animate="visible"
              exit="exit"
            >
              <motion.div 
                className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden"
                variants={modalVariants}
                initial="hidden"
                animate="visible"
                exit="exit"
                onClick={e => e.stopPropagation()}
              >
                {/* Header with image */}
                <div className="relative h-64 overflow-hidden">
                  <img 
                    src={selectedProject.image} 
                    alt={selectedProject.title} 
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent"></div>
                  <div className="absolute bottom-0 left-0 right-0 p-6">
                    <h2 className="text-2xl font-bold text-white mb-2">{selectedProject.title}</h2>
                    <div className="flex flex-wrap gap-1">
                      {selectedProject.tags.map(tag => (
                        <span 
                          key={`detail-${selectedProject.id}-${tag}`} 
                          className="text-xs px-2 py-1 bg-black/30 backdrop-blur-sm text-white rounded"
                        >
                          {projectTags.find(t => t.id === tag)?.name || tag}
                        </span>
                      ))}
                    </div>
                  </div>
                  
                  {/* Close button */}
                  <button 
                    className="absolute top-4 right-4 p-2 rounded-full bg-black/50 text-white hover:bg-black/70 transition-colors"
                    onClick={closeProjectDetail}
                    aria-label="Close project details"
                  >
                    <X className="h-5 w-5" />
                  </button>
                </div>
                
                {/* Tabs */}
                <div className="border-b border-gray-200 dark:border-gray-700">
                  <div className="flex overflow-x-auto scrollbar-hide">
                    <button
                      className={`px-4 py-3 font-medium text-sm border-b-2 transition-colors ${
                        activeTab === 'overview'
                          ? 'border-indigo-600 text-indigo-600 dark:border-indigo-400 dark:text-indigo-400'
                          : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
                      }`}
                      onClick={() => setActiveTab('overview')}
                    >
                      Overview
                    </button>
                    <button
                      className={`px-4 py-3 font-medium text-sm border-b-2 transition-colors ${
                        activeTab === 'technical'
                          ? 'border-indigo-600 text-indigo-600 dark:border-indigo-400 dark:text-indigo-400'
                          : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
                      }`}
                      onClick={() => setActiveTab('technical')}
                    >
                      Technical Details
                    </button>
                    <button
                      className={`px-4 py-3 font-medium text-sm border-b-2 transition-colors ${
                        activeTab === 'challenges'
                          ? 'border-indigo-600 text-indigo-600 dark:border-indigo-400 dark:text-indigo-400'
                          : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
                      }`}
                      onClick={() => setActiveTab('challenges')}
                    >
                      Challenges & Solutions
                    </button>
                    <button
                      className={`px-4 py-3 font-medium text-sm border-b-2 transition-colors ${
                        activeTab === 'results'
                          ? 'border-indigo-600 text-indigo-600 dark:border-indigo-400 dark:text-indigo-400'
                          : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
                      }`}
                      onClick={() => setActiveTab('results')}
                    >
                      Results
                    </button>
                  </div>
                </div>
                
                {/* Content */}
                <div className="p-6 overflow-y-auto max-h-[50vh]">
                  {/* Overview Tab */}
                  {activeTab === 'overview' && (
                    <div className="space-y-6">
                      <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                        {selectedProject.description}
                      </p>
                      
                      <div>
                        <h3 className="font-medium text-gray-900 dark:text-white text-lg mb-3 flex items-center">
                          <Award className="h-5 w-5 text-indigo-600 dark:text-indigo-400 mr-2" />
                          Key Achievements
                        </h3>
                        <ul className="grid grid-cols-1 md:grid-cols-2 gap-2">
                          {selectedProject.achievements.map((achievement, index) => (
                            <li 
                              key={index} 
                              className="flex items-start"
                            >
                              <span className="text-indigo-600 dark:text-indigo-400 mr-2">•</span>
                              <span className="text-gray-700 dark:text-gray-300">{achievement}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                      
                      <div className="flex flex-wrap gap-3 pt-4">
                        {selectedProject.liveUrl && (
                          <a 
                            href={selectedProject.liveUrl} 
                            target="_blank" 
                            rel="noopener noreferrer"
                            className="inline-flex items-center px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors"
                          >
                            <ExternalLink className="h-4 w-4 mr-2" />
                            View Live Project
                          </a>
                        )}
                        
                        {selectedProject.githubUrl && (
                          <a 
                            href={selectedProject.githubUrl} 
                            target="_blank" 
                            rel="noopener noreferrer"
                            className="inline-flex items-center px-4 py-2 bg-gray-800 text-white rounded-md hover:bg-gray-900 transition-colors"
                          >
                            <Github className="h-4 w-4 mr-2" />
                            GitHub Repository
                          </a>
                        )}
                        
                        {selectedProject.videoUrl && (
                          <a 
                            href={selectedProject.videoUrl} 
                            target="_blank" 
                            rel="noopener noreferrer"
                            className="inline-flex items-center px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
                          >
                            <PlayCircle className="h-4 w-4 mr-2" />
                            Watch Demo
                          </a>
                        )}
                      </div>
                    </div>
                  )}
                  
                  {/* Technical Tab */}
                  {activeTab === 'technical' && (
                    <div>
                      <div className="mb-6">
                        <h3 className="font-medium text-gray-900 dark:text-white text-lg mb-3 flex items-center">
                          <Code className="h-5 w-5 text-indigo-600 dark:text-indigo-400 mr-2" />
                          Technologies & Tools
                        </h3>
                        <div className="flex flex-wrap gap-2">
                          {selectedProject.technologies.map((tech, index) => (
                            <span 
                              key={index}
                              className="px-3 py-1 bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200 rounded-full text-sm"
                            >
                              {tech}
                            </span>
                          ))}
                        </div>
                      </div>
                      
                      <div className="prose prose-indigo dark:prose-invert max-w-none">
                        <p className="text-gray-700 dark:text-gray-300">
                          This project utilized a comprehensive technology stack carefully selected to meet the specific requirements and challenges. The combination of these technologies enabled us to create a robust, scalable, and user-friendly solution.
                        </p>
                        <p className="text-gray-700 dark:text-gray-300">
                          The architecture was designed with modularity and maintainability in mind, allowing for future extensions and enhancements without major refactoring.
                        </p>
                      </div>
                    </div>
                  )}
                  
                  {/* Challenges Tab */}
                  {activeTab === 'challenges' && (
                    <div className="space-y-8">
                      <div>
                        <h3 className="font-medium text-gray-900 dark:text-white text-lg mb-4 flex items-center">
                          <Activity className="h-5 w-5 text-orange-500 mr-2" />
                          Challenges Faced
                        </h3>
                        <ul className="space-y-3">
                          {selectedProject.challenges.map((challenge, index) => (
                            <li 
                              key={index} 
                              className="bg-orange-50 dark:bg-orange-900/20 p-3 rounded-md"
                            >
                              <p className="text-gray-800 dark:text-gray-200">{challenge}</p>
                            </li>
                          ))}
                        </ul>
                      </div>
                      
                      <div>
                        <h3 className="font-medium text-gray-900 dark:text-white text-lg mb-4 flex items-center">
                          <Code className="h-5 w-5 text-green-500 mr-2" />
                          Solutions Implemented
                        </h3>
                        <ul className="space-y-3">
                          {selectedProject.solutions.map((solution, index) => (
                            <li 
                              key={index} 
                              className="bg-green-50 dark:bg-green-900/20 p-3 rounded-md"
                            >
                              <p className="text-gray-800 dark:text-gray-200">{solution}</p>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  )}
                  
                  {/* Results Tab */}
                  {activeTab === 'results' && (
                    <div>
                      <h3 className="font-medium text-gray-900 dark:text-white text-lg mb-4 flex items-center">
                        <Award className="h-5 w-5 text-indigo-600 dark:text-indigo-400 mr-2" />
                        Project Outcomes
                      </h3>
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                        {selectedProject.achievements.map((achievement, index) => (
                          <div 
                            key={index}
                            className="bg-indigo-50 dark:bg-indigo-900/20 p-4 rounded-lg"
                          >
                            <p className="text-indigo-700 dark:text-indigo-300 font-medium">{achievement}</p>
                          </div>
                        ))}
                      </div>
                      
                      <h4 className="font-medium text-gray-900 dark:text-white mb-3">Additional Results</h4>
                      <ul className="space-y-2">
                        {selectedProject.results.map((result, index) => (
                          <li 
                            key={index}
                            className="flex items-start"
                          >
                            <ChevronRight className="h-5 w-5 text-green-500 mr-2 flex-shrink-0" />
                            <span className="text-gray-700 dark:text-gray-300">{result}</span>
                          </li>
                        ))}
                      </ul>
                      
                      <div className="mt-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <p className="text-gray-600 dark:text-gray-400 italic">
                          "This project demonstrates my ability to take complex challenges and deliver impactful solutions that drive measurable business results."
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </motion.div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </div>
  );
} 