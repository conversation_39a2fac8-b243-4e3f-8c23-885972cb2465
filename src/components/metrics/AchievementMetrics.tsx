'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, ArrowU<PERSON>, TrendingUp, DollarSign, Clock, Users, Award, Target, BookOpen } from 'lucide-react';

interface MetricCategory {
  id: string;
  title: string;
  icon: React.ReactNode;
}

interface Metric {
  id: string;
  title: string;
  value: string;
  increase: string;
  description: string;
  category: string;
  icon: React.ReactNode;
  color: string;
}

const categories: MetricCategory[] = [
  { id: 'all', title: 'All Metrics', icon: <BarChart className="h-5 w-5" /> },
  { id: 'financial', title: 'Financial Impact', icon: <DollarSign className="h-5 w-5" /> },
  { id: 'operational', title: 'Operational Efficiency', icon: <TrendingUp className="h-5 w-5" /> },
  { id: 'team', title: 'Team Leadership', icon: <Users className="h-5 w-5" /> },
  { id: 'personal', title: 'Personal Growth', icon: <Award className="h-5 w-5" /> },
];

const metrics: Metric[] = [
  // Financial Impact
  {
    id: 'cost-savings',
    title: 'Annual Cost Savings',
    value: '$100K+',
    increase: '25%',
    description: 'Implemented ERP features in-house, eliminating need for third-party systems',
    category: 'financial',
    icon: <DollarSign className="h-8 w-8" />,
    color: 'green'
  },
  {
    id: 'revenue-growth',
    title: 'E-commerce Revenue Growth',
    value: '25%',
    increase: '15%',
    description: 'Developed new Shopify website with improved UX and payment options',
    category: 'financial',
    icon: <TrendingUp className="h-8 w-8" />,
    color: 'emerald'
  },
  {
    id: 'inventory-cost',
    title: 'Inventory Cost Reduction',
    value: '20%',
    increase: '8%',
    description: 'Optimized inventory levels through data-driven forecasting',
    category: 'financial',
    icon: <DollarSign className="h-8 w-8" />,
    color: 'teal'
  },
  
  // Operational Efficiency
  {
    id: 'delivery-time',
    title: 'Delivery Time Reduction',
    value: '30%',
    increase: '12%',
    description: 'Implemented delivery management app with route optimization',
    category: 'operational',
    icon: <Clock className="h-8 w-8" />,
    color: 'blue'
  },
  {
    id: 'productivity',
    title: 'Team Productivity Increase',
    value: '35%',
    increase: '20%',
    description: 'Automated workflows and streamlined processes',
    category: 'operational',
    icon: <TrendingUp className="h-8 w-8" />,
    color: 'indigo'
  },
  {
    id: 'lead-time',
    title: 'Supply Chain Lead Time',
    value: '15%',
    increase: '7%',
    description: 'Improved vendor management and procurement processes',
    category: 'operational',
    icon: <Clock className="h-8 w-8" />,
    color: 'violet'
  },
  
  // Team Leadership
  {
    id: 'team-effectiveness',
    title: 'Team Effectiveness',
    value: '17%',
    increase: '5%',
    description: 'Mentored team members and implemented agile methodologies',
    category: 'team',
    icon: <Users className="h-8 w-8" />,
    color: 'purple'
  },
  {
    id: 'user-adoption',
    title: 'New System Adoption Rate',
    value: '92%',
    increase: '15%',
    description: 'Successfully onboarded users to new technologies through training',
    category: 'team',
    icon: <Users className="h-8 w-8" />,
    color: 'fuchsia'
  },
  {
    id: 'meeting-efficiency',
    title: 'Meeting Duration Reduction',
    value: '40%',
    increase: '25%',
    description: 'Implemented agile methodology with focused stand-ups',
    category: 'team',
    icon: <Clock className="h-8 w-8" />,
    color: 'pink'
  },
  
  // Personal Growth
  {
    id: 'certifications',
    title: 'Professional Certifications',
    value: '4',
    increase: '2',
    description: 'Earned certifications in product management and supply chain',
    category: 'personal',
    icon: <Award className="h-8 w-8" />,
    color: 'rose'
  },
  {
    id: 'projects',
    title: 'Projects Delivered',
    value: '12+',
    increase: '5',
    description: 'Successfully led and delivered various cross-functional projects',
    category: 'personal',
    icon: <Target className="h-8 w-8" />,
    color: 'orange'
  },
  {
    id: 'skills',
    title: 'New Tools Mastered',
    value: '8',
    increase: '3',
    description: 'Expanded technical toolkit with new software and methodologies',
    category: 'personal',
    icon: <BookOpen className="h-8 w-8" />,
    color: 'amber'
  }
];

export default function AchievementMetrics() {
  const [activeCategory, setActiveCategory] = useState('all');
  
  const filteredMetrics = activeCategory === 'all' 
    ? metrics 
    : metrics.filter(metric => metric.category === activeCategory);
  
  const fadeInVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: (i: number) => ({
      opacity: 1,
      y: 0,
      transition: {
        delay: i * 0.05,
        duration: 0.5
      }
    })
  };
  
  return (
    <div className="w-full bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
      <div className="p-6">
        <h2 className="text-xl font-bold text-gray-900 dark:text-white mb-2">Achievement Metrics</h2>
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          Key performance indicators and measurable achievements across my career.
        </p>
        
        {/* Categories */}
        <div className="flex flex-wrap gap-2 mb-8">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => setActiveCategory(category.id)}
              className={`flex items-center px-4 py-2 rounded-full text-sm font-medium transition-all ${
                activeCategory === category.id
                  ? 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
              }`}
            >
              <span className="mr-2">{category.icon}</span>
              {category.title}
            </button>
          ))}
        </div>
        
        {/* Metrics Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5">
          {filteredMetrics.map((metric, index) => (
            <motion.div
              key={metric.id}
              custom={index}
              variants={fadeInVariants}
              initial="hidden"
              animate="visible"
              className={`bg-${metric.color}-50 dark:bg-${metric.color}-900/20 rounded-lg p-5 hover:shadow-md transition-shadow relative overflow-hidden`}
            >
              <div className={`absolute top-0 right-0 w-24 h-24 opacity-10 rounded-full bg-${metric.color}-500 -mr-6 -mt-6`}></div>
              
              <div className="flex items-start justify-between">
                <div>
                  <h3 className={`font-semibold text-lg text-${metric.color}-700 dark:text-${metric.color}-300`}>
                    {metric.title}
                  </h3>
                  <div className="flex items-baseline mt-2">
                    <span className={`text-3xl font-bold text-${metric.color}-600 dark:text-${metric.color}-400`}>
                      {metric.value}
                    </span>
                    {metric.increase && (
                      <div className="ml-3 flex items-center text-green-600 dark:text-green-400">
                        <ArrowUp className="h-4 w-4 mr-1" />
                        <span className="text-sm font-medium">{metric.increase}</span>
                      </div>
                    )}
                  </div>
                </div>
                <div className={`p-3 rounded-full bg-${metric.color}-100 dark:bg-${metric.color}-800/50 text-${metric.color}-600 dark:text-${metric.color}-300`}>
                  {metric.icon}
                </div>
              </div>
              
              <p className="text-gray-600 dark:text-gray-300 mt-3 text-sm">
                {metric.description}
              </p>
              
              {/* Simple bar/graph visualization */}
              <div className="mt-4 w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                <motion.div
                  initial={{ width: 0 }}
                  animate={{ width: `${Math.min(parseInt(metric.value.replace(/\D/g, '')) || 50, 100)}%` }}
                  transition={{ duration: 1, delay: index * 0.1 }}
                  className={`h-full bg-${metric.color}-500 dark:bg-${metric.color}-400`}
                ></motion.div>
              </div>
            </motion.div>
          ))}
        </div>
        
        {/* Summary Stats */}
        <div className="mt-12 bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
          <h3 className="text-lg font-bold text-gray-900 dark:text-white mb-4">Career Impact Summary</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm">
              <div className="flex items-center mb-2">
                <DollarSign className="h-5 w-5 text-green-500 mr-2" />
                <h4 className="font-medium text-gray-900 dark:text-white">Financial Impact</h4>
              </div>
              <p className="text-3xl font-bold text-green-600 dark:text-green-400">$150K+</p>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">Annual savings & revenue growth</p>
            </div>
            
            <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm">
              <div className="flex items-center mb-2">
                <TrendingUp className="h-5 w-5 text-blue-500 mr-2" />
                <h4 className="font-medium text-gray-900 dark:text-white">Efficiency Gains</h4>
              </div>
              <p className="text-3xl font-bold text-blue-600 dark:text-blue-400">27%</p>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">Average operational improvement</p>
            </div>
            
            <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm">
              <div className="flex items-center mb-2">
                <Users className="h-5 w-5 text-purple-500 mr-2" />
                <h4 className="font-medium text-gray-900 dark:text-white">Team Growth</h4>
              </div>
              <p className="text-3xl font-bold text-purple-600 dark:text-purple-400">20+</p>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">Team members mentored</p>
            </div>
            
            <div className="bg-white dark:bg-gray-800 rounded-lg p-4 shadow-sm">
              <div className="flex items-center mb-2">
                <Target className="h-5 w-5 text-orange-500 mr-2" />
                <h4 className="font-medium text-gray-900 dark:text-white">Delivery Rate</h4>
              </div>
              <p className="text-3xl font-bold text-orange-600 dark:text-orange-400">95%</p>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">Projects delivered on time</p>
            </div>
          </div>
          
          <div className="mt-6 text-sm text-gray-600 dark:text-gray-400">
            <p>These metrics represent the cumulative impact across multiple roles and organizations. All figures are either directly measured or calculated based on company KPIs and performance evaluations.</p>
          </div>
        </div>
      </div>
    </div>
  );
} 