'use client';

import { motion } from 'framer-motion';
import { useRef } from 'react';
import {
  Terminal,
  Code,
  Database,
  Zap,
  TrendingUp,
  Users,
  Award,
  Cpu,
  BarChart3,
  Settings,
  User,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Globe,
  Linkedin,
  ExternalLink
} from 'lucide-react';

// Cyber feature cards data
const cyberFeatures = [
  {
    id: 'product-management',
    icon: <Terminal className="h-8 w-8" />,
    title: 'PRODUCT_MANAGEMENT.EXE',
    description: 'Led product development initiatives from conception to market launch, resulting in significant revenue growth.',
    status: 'ACTIVE',
    metrics: '7+ Years',
    color: 'primary'
  },
  {
    id: 'supply-chain',
    icon: <Database className="h-8 w-8" />,
    title: 'SUPPLY_CHAIN.OPT',
    description: 'Implemented data-driven supply chain strategies that reduced costs by 25% while improving delivery performance.',
    status: 'OPTIMIZED',
    metrics: '25% Cost Reduction',
    color: 'accent'
  },
  {
    id: 'team-leadership',
    icon: <Users className="h-8 w-8" />,
    title: 'TEAM_LEADERSHIP.SYS',
    description: 'Successfully managed diverse teams across multiple departments, fostering collaboration and driving success.',
    status: 'RUNNING',
    metrics: '20+ Team Members',
    color: 'secondary'
  },
  {
    id: 'innovation',
    icon: <Cpu className="h-8 w-8" />,
    title: 'INNOVATION.AI',
    description: 'Developed innovative solutions and strategic initiatives that enhanced operational efficiency.',
    status: 'DEPLOYED',
    metrics: 'Millions Saved',
    color: 'warning'
  },
  {
    id: 'data-analysis',
    icon: <BarChart3 className="h-8 w-8" />,
    title: 'DATA_ANALYSIS.PY',
    description: 'Leveraged advanced analytics and automation to drive data-driven decision making across organizations.',
    status: 'PROCESSING',
    metrics: '40% Performance Boost',
    color: 'primary'
  },
  {
    id: 'process-optimization',
    icon: <Settings className="h-8 w-8" />,
    title: 'PROCESS_OPT.CONFIG',
    description: 'Streamlined operations and implemented automation systems that significantly improved efficiency.',
    status: 'CONFIGURED',
    metrics: '30% Productivity Gain',
    color: 'accent'
  }
];

export default function AboutMe() {
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  return (
    <div className="space-responsive">
      {/* Terminal Profile Section */}
      <div className="grid-responsive-2 items-start">
        {/* Profile Terminal */}
        <motion.div
          initial={{ opacity: 0, x: -50 }}
          whileInView={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="terminal-window"
        >
          <div className="terminal-header">
            <div className="terminal-dot red"></div>
            <div className="terminal-dot yellow"></div>
            <div className="terminal-dot green"></div>
            <span className="ml-4 font-mono text-xs text-muted-foreground">profile.sh</span>
          </div>

          <div className="p-4 sm:p-6 font-mono text-xs sm:text-sm space-y-3">
            <div className="text-primary">
              <span className="text-muted-foreground">$</span> cat /usr/local/bin/magdi_profile.json
            </div>

            <div className="text-foreground space-y-2 pl-2 sm:pl-4">
              <div className="break-words"><span className="text-accent">"name":</span> <span className="text-secondary">"Magdi Mohamed"</span>,</div>
              <div className="break-words"><span className="text-accent">"role":</span> <span className="text-secondary">"Product Manager & Supply Chain Specialist"</span>,</div>
              <div><span className="text-accent">"experience":</span> <span className="text-warning">"7+ years"</span>,</div>
              <div><span className="text-accent">"location":</span> <span className="text-secondary">"Cairo, Egypt"</span>,</div>
              <div className="break-all"><span className="text-accent">"email":</span> <span className="text-secondary">"<EMAIL>"</span>,</div>
              <div><span className="text-accent">"phone":</span> <span className="text-secondary">"+201501056664"</span>,</div>
              <div className="break-all"><span className="text-accent">"linkedin":</span> <span className="text-secondary">"linkedin.com/in/magdi-mohamed-a38383126"</span>,</div>
              <div className="break-words"><span className="text-accent">"status":</span> <span className="text-primary">"AVAILABLE_FOR_OPPORTUNITIES"</span></div>
            </div>

            <div className="text-primary mt-4">
              <span className="text-muted-foreground">$</span> echo $MISSION
            </div>
            <div className="text-secondary pl-2 sm:pl-4 break-words">
              "Transforming complex business challenges into streamlined solutions"
            </div>
          </div>
        </motion.div>

        {/* Avatar and Stats */}
        <motion.div
          initial={{ opacity: 0, x: 50 }}
          whileInView={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          viewport={{ once: true }}
          className="space-y-6"
        >
          {/* Cyber Avatar */}
          <div className="relative mx-auto w-48 h-48 sm:w-56 sm:h-56 lg:w-64 lg:h-64">
            <div className="absolute inset-0 bg-gradient-to-br from-primary via-accent to-secondary rounded-lg opacity-20 blur-xl"></div>
            <div className="relative w-full h-full cyber-card flex items-center justify-center">
              <div className="relative">
                <User className="w-24 h-24 sm:w-28 sm:h-28 lg:w-32 lg:h-32 text-primary" />
                <motion.div
                  className="absolute inset-0 bg-primary/20 rounded-full blur-lg"
                  animate={{
                    scale: [1, 1.2, 1],
                    opacity: [0.3, 0.6, 0.3],
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    ease: "easeInOut",
                  }}
                />
              </div>
            </div>
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-2 gap-3 sm:gap-4">
            {[
              { label: "PROJECTS", value: "50+", icon: <Terminal className="w-5 h-5" /> },
              { label: "TEAMS_LED", value: "20+", icon: <Users className="w-5 h-5" /> },
              { label: "COST_SAVED", value: "25%", icon: <TrendingUp className="w-5 h-5" /> },
              { label: "UPTIME", value: "99.9%", icon: <Zap className="w-5 h-5" /> },
            ].map((stat, index) => (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="cyber-card p-3 sm:p-4 text-center"
              >
                <div className="flex items-center justify-center mb-2 text-primary">
                  {stat.icon}
                </div>
                <div className="font-mono text-lg sm:text-xl lg:text-2xl font-bold text-foreground">{stat.value}</div>
                <div className="font-mono text-xs text-muted-foreground break-words">{stat.label}</div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>

      {/* Cyber Expertise Grid */}
      <div className="space-y-8">
        <div className="text-center">
          <h3 className="text-3xl font-bold font-mono text-foreground mb-4">
            <span className="text-primary">&gt;</span> CORE_EXPERTISE.DIR
          </h3>
          <p className="text-muted-foreground font-mono">
            // Scroll horizontally to explore system modules
          </p>
        </div>

        <div className="relative">
          {/* Scroll hint */}
          <div className="flex justify-center mb-4 sm:mb-6">
            <div className="flex items-center gap-2 sm:gap-3 text-xs sm:text-sm font-mono text-muted-foreground cyber-card px-3 sm:px-4 py-2">
              <Terminal className="w-3 h-3 sm:w-4 sm:h-4 text-primary" />
              <span className="hidden sm:inline">SCROLL_HORIZONTAL.execute()</span>
              <span className="sm:hidden">SCROLL →</span>
              <motion.div
                animate={{ x: [0, 8, 0] }}
                transition={{ duration: 2, repeat: Infinity }}
                className="text-primary"
              >
                →
              </motion.div>
            </div>
          </div>

          {/* Horizontal scrolling container */}
          <div
            ref={scrollContainerRef}
            className="overflow-x-auto scrollbar-hide pb-4 sm:pb-6"
            style={{ scrollBehavior: 'smooth' }}
          >
            <div className="flex gap-4 sm:gap-6 w-max px-4">
              {cyberFeatures.map((feature, index) => (
                <motion.div
                  key={feature.id}
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  whileHover={{ y: -8, scale: 1.02 }}
                  className="flex-shrink-0 w-72 sm:w-80 cyber-card group"
                >
                  {/* Status Bar */}
                  <div className="flex items-center justify-between p-3 sm:p-4 border-b border-border">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-primary rounded-full animate-pulse"></div>
                      <span className="font-mono text-xs text-primary">{feature.status}</span>
                    </div>
                    <div className="font-mono text-xs text-muted-foreground hidden sm:block">{feature.metrics}</div>
                  </div>

                  <div className="p-4 sm:p-6">
                    {/* Icon and title */}
                    <div className="flex items-start mb-4">
                      <div className="p-3 rounded-lg bg-primary/10 text-primary mr-4 group-hover:bg-primary/20 transition-colors">
                        {feature.icon}
                      </div>
                      <div className="flex-1">
                        <h4 className="text-lg font-bold font-mono text-foreground mb-1">{feature.title}</h4>
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 bg-accent rounded-full"></div>
                          <span className="text-xs font-mono text-accent uppercase">{feature.color}_MODULE</span>
                        </div>
                      </div>
                    </div>

                    {/* Description */}
                    <p className="text-muted-foreground leading-relaxed mb-6 text-sm">
                      {feature.description}
                    </p>

                    {/* Progress Bar */}
                    <div className="space-y-2">
                      <div className="flex justify-between text-xs font-mono">
                        <span className="text-muted-foreground">EFFICIENCY</span>
                        <span className="text-primary">95%</span>
                      </div>
                      <div className="w-full bg-muted rounded-full h-2">
                        <motion.div
                          className="h-2 bg-gradient-to-r from-primary to-accent rounded-full"
                          initial={{ width: 0 }}
                          whileInView={{ width: '95%' }}
                          transition={{ duration: 1.5, delay: index * 0.2 }}
                          viewport={{ once: true }}
                        />
                      </div>
                    </div>

                    {/* Footer */}
                    <div className="flex items-center justify-between mt-4 pt-4 border-t border-border">
                      <div className="flex items-center gap-2 text-xs font-mono text-muted-foreground">
                        <Code className="w-3 h-3" />
                        <span>ACTIVE</span>
                      </div>
                      <motion.div
                        className="text-primary opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                        whileHover={{ x: 5 }}
                      >
                        <ExternalLink className="h-4 w-4" />
                      </motion.div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Cyber Story Section */}
      <div className="space-y-8">
        <div className="text-center">
          <h3 className="text-3xl font-bold font-mono text-foreground mb-4">
            <span className="text-primary">&gt;</span> STORY.MD
          </h3>
          <p className="text-muted-foreground font-mono">
            // Personal narrative and professional journey
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="terminal-window"
          >
            <div className="terminal-header">
              <div className="terminal-dot red"></div>
              <div className="terminal-dot yellow"></div>
              <div className="terminal-dot green"></div>
              <span className="ml-4 font-mono text-xs text-muted-foreground">background.log</span>
            </div>

            <div className="p-6 font-mono text-sm space-y-4 text-muted-foreground">
              <div className="text-primary">
                <span className="text-muted-foreground">[INFO]</span> INITIALIZING_CAREER.exe
              </div>

              <p>
                I'm a passionate Product Manager and Supply Chain Specialist with a background in IT. My journey
                began with a Bachelor's degree in Computers and Information Technology, which gave me a solid
                foundation in technical knowledge that I've since applied to solving complex business problems.
              </p>

              <p>
                Throughout my career at companies like Zawaya, Capiter, and SWVL, I've consistently demonstrated
                my ability to optimize supply chain operations, implement data-driven forecasting techniques, and
                lead cross-functional teams to success.
              </p>

              <div className="text-accent">
                <span className="text-muted-foreground">[SUCCESS]</span> CAREER_PROGRESSION: 100%
              </div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            className="terminal-window"
          >
            <div className="terminal-header">
              <div className="terminal-dot red"></div>
              <div className="terminal-dot yellow"></div>
              <div className="terminal-dot green"></div>
              <span className="ml-4 font-mono text-xs text-muted-foreground">achievements.log</span>
            </div>

            <div className="p-6 font-mono text-sm space-y-4 text-muted-foreground">
              <div className="text-secondary">
                <span className="text-muted-foreground">[EXEC]</span> ANALYZING_IMPACT.py
              </div>

              <p>
                My approach combines analytical thinking with practical implementation, allowing me to identify
                opportunities for improvement and develop effective solutions. I'm particularly proud of my work
                in fraud detection and prevention, which has saved companies millions in potential losses.
              </p>

              <p>
                When I'm not optimizing supply chains or developing new products, you might find me watching anime,
                playing MORPG games, or spending quality time with my family. I'm also continuously expanding my
                knowledge of analytical languages to further enhance my professional capabilities.
              </p>

              <div className="text-warning">
                <span className="text-muted-foreground">[OUTPUT]</span> IMPACT_GENERATED: MILLIONS_SAVED
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
}