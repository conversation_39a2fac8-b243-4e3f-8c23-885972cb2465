'use client'

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import ResponsiveGrid from './ResponsiveGrid';
import {
  Terminal,
  Code,
  Database,
  Zap,
  TrendingUp,
  Users,
  Award,
  Cpu,
  BarChart3,
  Settings,
  ExternalLink,
  Github,
  Globe,
  Calendar,
  MapPin
} from 'lucide-react';

// Cyber project data structure
interface CyberProject {
  id: string;
  title: string;
  type: string;
  status: string;
  description: string;
  technologies: string[];
  impact: string;
  metrics: string;
  icon: React.ReactNode;
  color: string;
}

// Cyber projects data
const cyberProjects: CyberProject[] = [
  {
    id: 'flutter-delivery-app',
    title: 'DELIVERY_TRACKER.FLUTTER',
    type: 'MOBILE_APPLICATION',
    status: 'DEPLOYED',
    description: 'Led the creation of an internal Flutter delivery app enabling real-time tracking of delivery agents performance and availability.',
    technologies: ['Flutter', 'Dart', 'Firebase', 'Real-time APIs'],
    impact: 'Streamlined delivery operations and improved agent management',
    metrics: '100% deployment success',
    icon: <Terminal className="w-6 h-6" />,
    color: 'primary'
  },
  {
    id: 'shopify-ecommerce',
    title: 'ECOMMERCE_PLATFORM.SHOPIFY',
    type: 'WEB_APPLICATION',
    status: 'ACTIVE',
    description: 'Developed a new Shopify website, increasing customer orders by 25% and improving overall user experience.',
    technologies: ['Shopify', 'JavaScript', 'CSS', 'Payment APIs'],
    impact: 'Enhanced customer experience and increased sales',
    metrics: '25% order increase',
    icon: <Globe className="w-6 h-6" />,
    color: 'accent'
  },
  {
    id: 'crm-system',
    title: 'CRM_MANAGEMENT.SYS',
    type: 'WEB_APPLICATION',
    status: 'OPTIMIZED',
    description: 'Created a CRM web application that streamlined operations for sales and finance teams in managing cash flow and customer relationships.',
    technologies: ['React', 'Node.js', 'Database', 'APIs'],
    impact: 'Improved team efficiency and customer management',
    metrics: '30% productivity boost',
    icon: <Database className="w-6 h-6" />,
    color: 'secondary'
  },
  {
    id: 'erp-integration',
    title: 'ERP_INTEGRATION.MODULE',
    type: 'SYSTEM_INTEGRATION',
    status: 'RUNNING',
    description: 'Successfully implemented ERP features directly into the system, saving $100K annually by eliminating reliance on third-party ERP solutions.',
    technologies: ['ERP Systems', 'APIs', 'Database Integration'],
    impact: 'Cost reduction and system efficiency',
    metrics: '$100K annual savings',
    icon: <Settings className="w-6 h-6" />,
    color: 'warning'
  },
  {
    id: 'automation-system',
    title: 'AUTOMATION_ENGINE.AI',
    type: 'PROCESS_AUTOMATION',
    status: 'PROCESSING',
    description: 'Implemented an Automation system, optimizing team efficiency and streamlining tasks, resulting in a 30% increase in overall productivity.',
    technologies: ['Python', 'Automation Tools', 'AI/ML'],
    impact: 'Enhanced operational efficiency',
    metrics: '30% productivity increase',
    icon: <Cpu className="w-6 h-6" />,
    color: 'primary'
  },
  {
    id: 'fraud-detection',
    title: 'FRAUD_DETECTION.SECURITY',
    type: 'SECURITY_SYSTEM',
    status: 'MONITORING',
    description: 'Developed fraud detection and prevention mechanisms, saving companies millions in potential losses through advanced monitoring.',
    technologies: ['Data Analytics', 'Machine Learning', 'Security APIs'],
    impact: 'Risk mitigation and loss prevention',
    metrics: 'Millions saved',
    icon: <BarChart3 className="w-6 h-6" />,
    color: 'accent'
  }
];

const Portfolio: React.FC = () => {

  return (
    <div className="space-responsive">
      {/* Cyber Projects Grid */}
      <ResponsiveGrid
        cols={{ xs: 1, sm: 2, lg: 3 }}
        gap="md"
        animate={true}
        stagger={0.1}
      >
        {cyberProjects.map((project, index) => (
          <motion.div
            key={project.id}
            whileHover={{ y: -8, scale: 1.02 }}
            className="cyber-card group h-full"
          >
            {/* Project Status Bar */}
            <div className="flex items-center justify-between p-3 sm:p-4 border-b border-border">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-primary rounded-full animate-pulse"></div>
                <span className="font-mono text-xs text-primary">{project.status}</span>
              </div>
              <div className="font-mono text-xs text-muted-foreground hidden sm:block">{project.type}</div>
            </div>

            <div className="p-4 sm:p-6">
              {/* Project Icon and Title */}
              <div className="flex items-start mb-4">
                <div className="p-2 sm:p-3 rounded-lg bg-primary/10 text-primary mr-3 sm:mr-4 group-hover:bg-primary/20 transition-colors flex-shrink-0">
                  {project.icon}
                </div>
                <div className="flex-1 min-w-0">
                  <h3 className="text-base sm:text-lg font-bold font-mono text-foreground mb-1 break-words">{project.title}</h3>
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-accent rounded-full"></div>
                    <span className="text-xs font-mono text-accent uppercase truncate">{project.color}_MODULE</span>
                  </div>
                </div>
              </div>

              {/* Project Description */}
              <p className="text-muted-foreground text-sm leading-relaxed mb-4">
                {project.description}
              </p>

              {/* Technologies */}
              <div className="mb-4">
                <div className="text-xs font-mono text-muted-foreground mb-2">TECH_STACK:</div>
                <div className="flex flex-wrap gap-1">
                  {project.technologies.map((tech, techIndex) => (
                    <span
                      key={techIndex}
                      className="px-2 py-1 bg-muted rounded text-xs font-mono text-muted-foreground"
                    >
                      {tech}
                    </span>
                  ))}
                </div>
              </div>

              {/* Impact Metrics */}
              <div className="space-y-2 mb-4">
                <div className="flex justify-between text-xs font-mono">
                  <span className="text-muted-foreground">IMPACT</span>
                  <span className="text-primary">{project.metrics}</span>
                </div>
                <div className="w-full bg-muted rounded-full h-2">
                  <motion.div
                    className="h-2 bg-gradient-to-r from-primary to-accent rounded-full"
                    initial={{ width: 0 }}
                    whileInView={{ width: '90%' }}
                    transition={{ duration: 1.5, delay: index * 0.2 }}
                    viewport={{ once: true }}
                  />
                </div>
              </div>

              {/* Project Footer */}
              <div className="flex items-center justify-between pt-4 border-t border-border">
                <div className="flex items-center gap-2 text-xs font-mono text-muted-foreground">
                  <Code className="w-3 h-3" />
                  <span>DEPLOYED</span>
                </div>
                <motion.div
                  className="text-primary opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                  whileHover={{ x: 5 }}
                >
                  <ExternalLink className="h-4 w-4" />
                </motion.div>
              </div>
            </div>
          </motion.div>
        ))}
      </ResponsiveGrid>
    </div>
  );
};

export default Portfolio;