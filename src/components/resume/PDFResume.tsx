'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FileDown, Download, Check, X, Eye, EyeOff, Settings, FileText, Printer, Plus, Calendar, Mail, Phone, MapPin, Globe, Linkedin, Trash } from 'lucide-react';

interface ResumeSection {
  id: string;
  name: string;
  icon: React.ReactNode;
  included: boolean;
  description?: string;
}

export default function PDFResume() {
  const [isCustomizing, setIsCustomizing] = useState(false);
  const [pdfGenerating, setPdfGenerating] = useState(false);
  const [pdfGenerated, setPdfGenerated] = useState(false);
  const [viewMode, setViewMode] = useState<'preview' | 'customize'>('preview');
  const [resumeSections, setResumeSections] = useState<ResumeSection[]>([
    { id: 'profile', name: 'Professional Profile', icon: <FileText className="h-4 w-4" />, included: true, description: 'A concise summary of your professional background and career objectives.' },
    { id: 'experience', name: 'Work Experience', icon: <Briefcase className="h-4 w-4" />, included: true, description: 'Detailed history of your professional roles and accomplishments.' },
    { id: 'education', name: 'Education', icon: <GraduationCap className="h-4 w-4" />, included: true, description: 'Your academic qualifications and relevant coursework.' },
    { id: 'skills', name: 'Skills', icon: <Code className="h-4 w-4" />, included: true, description: 'Technical and soft skills that showcase your capabilities.' },
    { id: 'projects', name: 'Selected Projects', icon: <FolderKanban className="h-4 w-4" />, included: true, description: 'Highlighted projects that demonstrate your expertise.' },
    { id: 'achievements', name: 'Achievements', icon: <Trophy className="h-4 w-4" />, included: true, description: 'Notable accomplishments and recognition.' },
    { id: 'tools', name: 'Tools & Technologies', icon: <Wrench className="h-4 w-4" />, included: true, description: 'Software, platforms, and technologies you\'re proficient with.' },
    { id: 'certificates', name: 'Certifications', icon: <Award className="h-4 w-4" />, included: true, description: 'Professional certifications and qualifications.' },
    { id: 'languages', name: 'Languages', icon: <Globe className="h-4 w-4" />, included: true, description: 'Languages you speak and your proficiency levels.' },
    { id: 'interests', name: 'Interests', icon: <Heart className="h-4 w-4" />, included: false, description: 'Personal interests that add dimension to your profile.' }
  ]);

  const toggleSection = (id: string) => {
    setResumeSections(prevSections =>
      prevSections.map(section =>
        section.id === id ? { ...section, included: !section.included } : section
      )
    );
  };

  const generatePDF = () => {
    setPdfGenerating(true);
    
    // Simulate PDF generation delay
    setTimeout(() => {
      setPdfGenerating(false);
      setPdfGenerated(true);
      
      // Reset generated state after a delay
      setTimeout(() => {
        setPdfGenerated(false);
      }, 3000);
    }, 2000);
  };

  // Calculate metrics
  const totalSections = resumeSections.length;
  const includedSections = resumeSections.filter(s => s.included).length;
  const completionPercentage = Math.round((includedSections / totalSections) * 100);

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-xl overflow-hidden">
      {/* Header with tabs */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <div className="flex">
          <button
            onClick={() => setViewMode('preview')}
            className={`py-4 px-6 font-medium text-sm focus:outline-none transition-colors ${
              viewMode === 'preview'
                ? 'text-indigo-600 dark:text-indigo-400 border-b-2 border-indigo-600 dark:border-indigo-400'
                : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
            }`}
          >
            <div className="flex items-center">
              <Eye className="w-4 h-4 mr-2" />
              Preview
            </div>
          </button>
          <button
            onClick={() => setViewMode('customize')}
            className={`py-4 px-6 font-medium text-sm focus:outline-none transition-colors ${
              viewMode === 'customize'
                ? 'text-indigo-600 dark:text-indigo-400 border-b-2 border-indigo-600 dark:border-indigo-400'
                : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
            }`}
          >
            <div className="flex items-center">
              <Settings className="w-4 h-4 mr-2" />
              Customize
            </div>
          </button>
        </div>
      </div>
      
      <div className="p-6">
        {/* Preview Mode */}
        {viewMode === 'preview' && (
          <div>
            <div className="bg-indigo-50 dark:bg-indigo-900/20 rounded-lg p-4 mb-6">
              <div className="flex items-start">
                <div className="bg-indigo-100 dark:bg-indigo-800/40 p-2 rounded-full mr-3">
                  <FileDown className="h-5 w-5 text-indigo-600 dark:text-indigo-400" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900 dark:text-white">Tailored Resume Builder</h3>
                  <p className="text-gray-700 dark:text-gray-300 text-sm mt-1">
                    Generate a professional PDF resume customized to your needs. Your resume will include
                    {includedSections === totalSections ? ' all' : ` ${includedSections} of ${totalSections}`} sections
                    and be perfectly formatted for sharing with potential employers.
                  </p>
                </div>
              </div>
            </div>
            
            {/* Resume Preview */}
            <div className="border border-gray-200 dark:border-gray-700 rounded-xl overflow-hidden mb-6">
              {/* Preview Header */}
              <div className="bg-gradient-to-r from-indigo-600 to-indigo-800 py-3 px-4 text-white">
                <div className="flex justify-between items-center">
                  <h3 className="font-medium">Resume Preview</h3>
                  <div className="flex items-center text-xs">
                    <span className="mr-2">Sections: {includedSections}/{totalSections}</span>
                    <div className="w-20 h-2 bg-white/30 rounded-full overflow-hidden">
                      <div 
                        className="h-full bg-white rounded-full"
                        style={{ width: `${completionPercentage}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Preview Content */}
              <div className="p-6 flex justify-center">
                <div className="w-full max-w-lg">
                  <div className="aspect-[8.5/11] bg-white dark:bg-gray-900 rounded-lg shadow-lg overflow-hidden border border-gray-200 dark:border-gray-700">
                    {/* Resume Header */}
                    <div className="bg-indigo-600 dark:bg-indigo-800 p-6 text-white">
                      <h3 className="text-xl font-bold">Magdi Mohamed</h3>
                      <p className="text-indigo-100">Product Manager & Supply Chain Specialist</p>
                      <div className="flex flex-wrap text-xs mt-2 gap-3">
                        <div className="flex items-center">
                          <Mail className="w-3 h-3 mr-1" />
                          <span><EMAIL></span>
                        </div>
                        <div className="flex items-center">
                          <Phone className="w-3 h-3 mr-1" />
                          <span>+201501056664</span>
                        </div>
                        <div className="flex items-center">
                          <MapPin className="w-3 h-3 mr-1" />
                          <span>Cairo, Egypt</span>
                        </div>
                      </div>
                    </div>
                    
                    {/* Resume Content */}
                    <div className="p-5 space-y-4">
                      {resumeSections
                        .filter(section => section.included)
                        .map((section, index) => (
                          <div key={section.id} className="space-y-2">
                            <div className="flex items-center">
                              <div className="p-1 bg-indigo-100 dark:bg-indigo-900/30 rounded mr-2">
                                {section.icon}
                              </div>
                              <h4 className="text-sm font-semibold text-gray-900 dark:text-white">{section.name}</h4>
                            </div>
                            
                            {section.id === 'profile' && (
                              <div className="pl-6">
                                <div className="h-2 bg-gray-200 dark:bg-gray-700 rounded w-full mb-1"></div>
                                <div className="h-2 bg-gray-200 dark:bg-gray-700 rounded w-5/6 mb-1"></div>
                                <div className="h-2 bg-gray-200 dark:bg-gray-700 rounded w-4/6"></div>
                              </div>
                            )}
                            
                            {section.id === 'experience' && (
                              <div className="pl-6 space-y-3">
                                <div>
                                  <div className="flex justify-between mb-1">
                                    <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded w-1/3"></div>
                                    <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/6"></div>
                                  </div>
                                  <div className="h-2 bg-gray-200 dark:bg-gray-700 rounded w-full mb-1"></div>
                                  <div className="h-2 bg-gray-200 dark:bg-gray-700 rounded w-5/6"></div>
                                </div>
                                <div>
                                  <div className="flex justify-between mb-1">
                                    <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded w-2/5"></div>
                                    <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/6"></div>
                                  </div>
                                  <div className="h-2 bg-gray-200 dark:bg-gray-700 rounded w-full mb-1"></div>
                                  <div className="h-2 bg-gray-200 dark:bg-gray-700 rounded w-4/6"></div>
                                </div>
                              </div>
                            )}
                            
                            {section.id !== 'profile' && section.id !== 'experience' && (
                              <div className="pl-6">
                                <div className="h-2 bg-gray-200 dark:bg-gray-700 rounded w-full mb-1"></div>
                                <div className="h-2 bg-gray-200 dark:bg-gray-700 rounded w-5/6 mb-1"></div>
                                <div className="h-2 bg-gray-200 dark:bg-gray-700 rounded w-4/6"></div>
                              </div>
                            )}
                            
                            {index < resumeSections.filter(s => s.included).length - 1 && (
                              <div className="h-px bg-gray-200 dark:bg-gray-700 w-full my-2"></div>
                            )}
                          </div>
                        ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            {/* Generate Buttons */}
            <div className="flex flex-col sm:flex-row gap-4">
              <button
                onClick={generatePDF}
                disabled={pdfGenerating}
                className={`flex-1 flex items-center justify-center px-4 py-3 rounded-lg transition-colors ${
                  pdfGenerating || pdfGenerated
                    ? 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400'
                    : 'bg-indigo-600 text-white hover:bg-indigo-700'
                }`}
              >
                {pdfGenerating ? (
                  <>
                    <div className="animate-spin h-5 w-5 mr-2 border-2 border-t-transparent border-white dark:border-indigo-400 rounded-full"></div>
                    Generating Your Resume...
                  </>
                ) : pdfGenerated ? (
                  <>
                    <Check className="h-5 w-5 mr-2" />
                    Resume Generated Successfully
                  </>
                ) : (
                  <>
                    <Download className="h-5 w-5 mr-2" />
                    Generate Resume PDF
                  </>
                )}
              </button>
              
              <a
                href="#"
                className="flex-1 flex items-center justify-center px-4 py-3 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
                onClick={(e) => e.preventDefault()}
              >
                <Printer className="h-5 w-5 mr-2" />
                Print Resume
              </a>
            </div>
          </div>
        )}
        
        {/* Customize Mode */}
        {viewMode === 'customize' && (
          <div>
            <div className="mb-6">
              <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">Customize Your Resume</h3>
              <p className="text-gray-600 dark:text-gray-400">
                Select which sections to include in your resume. Customize it based on the job you're applying for.
              </p>
              
              {/* Progress Bar */}
              <div className="mt-4 mb-6">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Resume Completion</span>
                  <span className="text-sm font-medium text-indigo-600 dark:text-indigo-400">{completionPercentage}%</span>
                </div>
                <div className="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                  <div 
                    className="h-full bg-indigo-600 dark:bg-indigo-500 rounded-full transition-all duration-500"
                    style={{ width: `${completionPercentage}%` }}
                  ></div>
                </div>
              </div>
            </div>
            
            {/* Section Toggles */}
            <div className="space-y-4">
              {resumeSections.map((section) => (
                <motion.div
                  key={section.id}
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3 }}
                  className={`border rounded-xl overflow-hidden transition-colors ${
                    section.included 
                      ? 'border-indigo-200 dark:border-indigo-800 bg-indigo-50 dark:bg-indigo-900/20' 
                      : 'border-gray-200 dark:border-gray-700'
                  }`}
                >
                  <div className="p-4 flex items-center justify-between">
                    <div className="flex items-center">
                      <div className={`p-2 rounded-lg mr-3 ${
                        section.included 
                          ? 'bg-indigo-100 dark:bg-indigo-800/40 text-indigo-600 dark:text-indigo-400' 
                          : 'bg-gray-100 dark:bg-gray-800 text-gray-500 dark:text-gray-400'
                      }`}>
                        {section.icon}
                      </div>
                      <div>
                        <h4 className="font-medium text-gray-900 dark:text-white">{section.name}</h4>
                        {section.description && (
                          <p className="text-sm text-gray-600 dark:text-gray-400 mt-0.5">{section.description}</p>
                        )}
                      </div>
                    </div>
                    
                    <div className="flex items-center">
                      <button
                        onClick={() => toggleSection(section.id)}
                        className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                          section.included ? 'bg-indigo-600' : 'bg-gray-300 dark:bg-gray-600'
                        }`}
                      >
                        <span
                          className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                            section.included ? 'translate-x-6' : 'translate-x-1'
                          }`}
                        />
                      </button>
                    </div>
                  </div>
                </motion.div>
              ))}
              
              {/* Add Custom Section */}
              <div className="mt-4 border border-dashed border-gray-300 dark:border-gray-600 rounded-xl p-4 text-center">
                <button 
                  className="inline-flex items-center text-indigo-600 dark:text-indigo-400 hover:text-indigo-700 dark:hover:text-indigo-300"
                  onClick={() => {/* Add functionality here */}}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Custom Section
                </button>
              </div>
            </div>
            
            {/* Action Buttons */}
            <div className="mt-8 flex justify-end space-x-4">
              <button
                onClick={() => setViewMode('preview')}
                className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={() => setViewMode('preview')}
                className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
              >
                Apply Changes
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

// Icons
function Briefcase(props: any) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" {...props}><rect width="20" height="14" x="2" y="7" rx="2" ry="2"/><path d="M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16"/></svg>
  );
}

function GraduationCap(props: any) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" {...props}><path d="M22 10v6M2 10l10-5 10 5-10 5z"/><path d="M6 12v5c0 2 2 3 6 3s6-1 6-3v-5"/></svg>
  );
}

function Code(props: any) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" {...props}><polyline points="16 18 22 12 16 6"/><polyline points="8 6 2 12 8 18"/></svg>
  );
}

function FolderKanban(props: any) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" {...props}><path d="M4 20h16a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.93a2 2 0 0 1-1.66-.9l-.82-1.2A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13c0 1.1.9 2 2 2Z"/><path d="M8 10v4"/><path d="M12 10v2"/><path d="M16 10v6"/></svg>
  );
}

function Trophy(props: any) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" {...props}><path d="M6 9H4.5a2.5 2.5 0 0 1 0-5H6"/><path d="M18 9h1.5a2.5 2.5 0 0 0 0-5H18"/><path d="M4 22h16"/><path d="M10 14.66V17c0 .55-.47.98-.97 1.21C7.85 18.75 7 20.24 7 22"/><path d="M14 14.66V17c0 .55.47.98.97 1.21C16.15 18.75 17 20.24 17 22"/><path d="M18 2H6v7a6 6 0 0 0 12 0V2Z"/></svg>
  );
}

function Wrench(props: any) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" {...props}><path d="M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z"/></svg>
  );
}

function Award(props: any) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" {...props}><circle cx="12" cy="8" r="7"/><polyline points="8.21 13.89 7 23 12 20 17 23 15.79 13.88"/></svg>
  );
}

function Heart(props: any) {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" {...props}><path d="M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z"/></svg>
  );
} 