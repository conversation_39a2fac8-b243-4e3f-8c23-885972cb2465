'use client';

import { useEffect, useRef } from 'react';
import { motion, useAnimation } from 'framer-motion';
import { gsap } from 'gsap';
import { Terminal, Code, Zap, ArrowDown, Github, Linkedin, Mail } from 'lucide-react';

export default function HeroSection() {
  const heroRef = useRef<HTMLDivElement>(null);
  const controls = useAnimation();

  useEffect(() => {
    const ctx = gsap.context(() => {
      // Floating particles animation
      gsap.to('.particle', {
        y: 'random(-100, 100)',
        x: 'random(-100, 100)',
        duration: 'random(2, 4)',
        repeat: -1,
        yoyo: true,
        ease: 'power2.inOut',
        stagger: 0.2,
      });

      // Terminal cursor blink
      gsap.to('.cursor', {
        opacity: 0,
        duration: 0.5,
        repeat: -1,
        yoyo: true,
        ease: 'power2.inOut',
      });

      // Glitch effect on title
      gsap.to('.glitch-title', {
        x: 'random(-2, 2)',
        duration: 0.1,
        repeat: -1,
        yoyo: true,
        ease: 'power2.inOut',
      });
    }, heroRef);

    return () => ctx.revert();
  }, []);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        delayChildren: 0.3,
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 100,
      },
    },
  };

  return (
    <section 
      ref={heroRef}
      className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-background via-background to-muted"
    >
      {/* Animated Background Grid */}
      <div className="absolute inset-0 cyber-grid opacity-30" />
      
      {/* Floating Particles */}
      <div className="absolute inset-0 overflow-hidden">
        {[...Array(20)].map((_, i) => (
          <div
            key={i}
            className="particle absolute w-1 h-1 bg-primary rounded-full opacity-60"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
          />
        ))}
      </div>

      {/* Main Content */}
      <motion.div
        className="relative z-10 container-wide text-center"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {/* Terminal Window */}
        <motion.div
          className="terminal-window max-w-4xl mx-auto mb-8 sm:mb-12"
          variants={itemVariants}
        >
          <div className="terminal-header">
            <div className="terminal-dot red"></div>
            <div className="terminal-dot yellow"></div>
            <div className="terminal-dot green"></div>
            <span className="ml-4 font-mono text-xs text-muted-foreground">magdi@portfolio:~$</span>
          </div>

          <div className="p-4 sm:p-6 lg:p-8 font-mono text-xs sm:text-sm text-left">
            <div className="text-primary mb-2">
              <span className="text-muted-foreground">$</span> whoami
            </div>
            <div className="text-foreground mb-4 break-words">
              Magdi Mohamed - Product Manager & Supply Chain Specialist
            </div>

            <div className="text-primary mb-2">
              <span className="text-muted-foreground">$</span> cat skills.txt
            </div>
            <div className="text-foreground mb-4 break-words">
              Product Management | Supply Chain Optimization | Data Analysis | Process Improvement
            </div>

            <div className="text-primary mb-2">
              <span className="text-muted-foreground">$</span> echo $MISSION
            </div>
            <div className="text-foreground mb-4 break-words">
              "Transforming complex business challenges into streamlined solutions"
            </div>

            <div className="text-primary">
              <span className="text-muted-foreground">$</span> ./start_portfolio.sh
              <span className="cursor">|</span>
            </div>
          </div>
        </motion.div>

        {/* Main Title */}
        <motion.div variants={itemVariants} className="mb-6 sm:mb-8">
          <h1 className="hero-title mb-4">
            <span className="glitch-title block" data-text="MAGDI">
              <span className="bg-gradient-to-r from-primary via-accent to-secondary bg-clip-text text-transparent">
                MAGDI
              </span>
            </span>
            <span className="glitch-title block text-foreground" data-text="MOHAMED">
              MOHAMED
            </span>
          </h1>

          <div className="flex items-center justify-center gap-2 sm:gap-4 mb-6">
            <div className="h-px bg-gradient-to-r from-transparent via-primary to-transparent flex-1 max-w-16 sm:max-w-32" />
            <div className="flex items-center gap-1 sm:gap-2 font-mono text-primary">
              <Terminal className="w-4 h-4 sm:w-5 sm:h-5" />
              <span className="text-xs sm:text-sm tracking-wider">PRODUCT_MANAGER</span>
            </div>
            <div className="h-px bg-gradient-to-r from-transparent via-primary to-transparent flex-1 max-w-16 sm:max-w-32" />
          </div>
        </motion.div>

        {/* Subtitle */}
        <motion.p
          variants={itemVariants}
          className="hero-subtitle mb-8 sm:mb-12 lg:mb-16"
        >
          Bridging the gap between{' '}
          <span className="text-primary font-semibold">technology</span> and{' '}
          <span className="text-accent font-semibold">business strategy</span> to deliver{' '}
          <span className="text-secondary font-semibold">exceptional results</span>
        </motion.p>

        {/* CTA Buttons */}
        <motion.div
          variants={itemVariants}
          className="flex-responsive-center mb-12 sm:mb-16"
        >
          <motion.a
            href="#projects"
            className="cyber-btn group"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Code className="w-5 h-5 mr-2" />
            VIEW PROJECTS
            <Zap className="w-4 h-4 ml-2 group-hover:animate-pulse" />
          </motion.a>

          <motion.a
            href="/assets/magdi_mohamed_cv.pdf"
            download="Magdi_Mohamed_CV.pdf"
            target="_blank"
            rel="noopener noreferrer"
            className="cyber-btn-outline group"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Terminal className="w-5 h-5 mr-2" />
            DOWNLOAD CV
            <ArrowDown className="w-4 h-4 ml-2 group-hover:translate-y-1 transition-transform" />
          </motion.a>
        </motion.div>

        {/* Social Links */}
        <motion.div
          variants={itemVariants}
          className="flex justify-center gap-4 sm:gap-6"
        >
          {[
            { icon: Github, href: 'https://github.com/magdi-mohamed', label: 'GitHub' },
            { icon: Linkedin, href: 'https://linkedin.com/in/magdi-mohamed', label: 'LinkedIn' },
            { icon: Mail, href: 'mailto:<EMAIL>', label: 'Email' },
          ].map((social, index) => (
            <motion.a
              key={social.label}
              href={social.href}
              target={social.href.startsWith('mailto:') ? '_self' : '_blank'}
              rel={social.href.startsWith('mailto:') ? '' : 'noopener noreferrer'}
              className="group relative p-2 sm:p-3 rounded-lg border border-border hover:border-primary/50 transition-all duration-300"
              whileHover={{ scale: 1.1, rotate: 5 }}
              whileTap={{ scale: 0.95 }}
            >
              <social.icon className="w-5 h-5 sm:w-6 sm:h-6 text-muted-foreground group-hover:text-primary transition-colors" />
              <span className="absolute -top-8 left-1/2 transform -translate-x-1/2 text-xs font-mono text-primary opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                {social.label}
              </span>
            </motion.a>
          ))}
        </motion.div>
      </motion.div>

      {/* Scroll Indicator */}
      <motion.a
        href="#about"
        className="absolute bottom-16 left-1/2 transform -translate-x-1/2 cursor-pointer group"
        animate={{
          y: [0, 10, 0],
        }}
        transition={{
          duration: 2,
          repeat: Infinity,
          ease: 'easeInOut',
        }}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.95 }}
      >
        <div className="flex flex-col items-center gap-2 text-muted-foreground group-hover:text-primary transition-colors">
          <span className="font-mono text-xs tracking-wider">SCROLL</span>
          <ArrowDown className="w-4 h-4 group-hover:translate-y-1 transition-transform" />
        </div>
      </motion.a>
    </section>
  );
}
