'use client';

import { useState, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Wrench, BarChart4, Workflow, Users, PieChart, Code, Globe, Truck, Database, Search, Star, ChevronRight, Check } from 'lucide-react';
import { MotionWrapper } from '../animations/MotionWrapper';

type ToolCategory = 'product' | 'supply' | 'analytics' | 'collaboration' | 'all';

interface ToolItem {
  id: string;
  name: string;
  description: string;
  category: ToolCategory[];
  icon: React.ReactNode;
  proficiency: number; // 1-5 scale
}

const toolsData: ToolItem[] = [
  // Product Management Tools
  {
    id: 'jira',
    name: '<PERSON><PERSON>',
    description: 'Project tracking, issue management, and agile planning.',
    category: ['product', 'collaboration'],
    icon: <Workflow className="h-6 w-6" />,
    proficiency: 5
  },
  {
    id: 'figma',
    name: 'Figma',
    description: 'UI/UX design, prototyping, and collaboration.',
    category: ['product'],
    icon: <Wrench className="h-6 w-6" />,
    proficiency: 4
  },
  {
    id: 'product-board',
    name: 'ProductBoard',
    description: 'Product management solution for roadmapping and prioritization.',
    category: ['product'],
    icon: <BarChart4 className="h-6 w-6" />,
    proficiency: 4
  },
  
  // Supply Chain Tools
  {
    id: 'sap',
    name: 'SAP',
    description: 'Enterprise resource planning and supply chain management.',
    category: ['supply'],
    icon: <Database className="h-6 w-6" />,
    proficiency: 4
  },
  {
    id: 'odoo',
    name: 'Odoo',
    description: 'Integrated business applications including inventory management.',
    category: ['supply', 'product'],
    icon: <Truck className="h-6 w-6" />,
    proficiency: 5
  },
  {
    id: 'netstock',
    name: 'Netstock',
    description: 'Inventory optimization and supply chain planning.',
    category: ['supply'],
    icon: <BarChart4 className="h-6 w-6" />,
    proficiency: 4
  },
  
  // Analytics Tools
  {
    id: 'power-bi',
    name: 'Power BI',
    description: 'Business analytics and data visualization.',
    category: ['analytics', 'product', 'supply'],
    icon: <PieChart className="h-6 w-6" />,
    proficiency: 5
  },
  {
    id: 'tableau',
    name: 'Tableau',
    description: 'Interactive data visualization for business intelligence.',
    category: ['analytics'],
    icon: <BarChart4 className="h-6 w-6" />,
    proficiency: 4
  },
  {
    id: 'excel',
    name: 'Advanced Excel',
    description: 'Data analysis, modeling, and reporting.',
    category: ['analytics', 'supply'],
    icon: <Database className="h-6 w-6" />,
    proficiency: 5
  },
  
  // Collaboration Tools
  {
    id: 'slack',
    name: 'Slack',
    description: 'Team communication and collaboration platform.',
    category: ['collaboration'],
    icon: <Users className="h-6 w-6" />,
    proficiency: 5
  },
  {
    id: 'notion',
    name: 'Notion',
    description: 'All-in-one workspace for notes, tasks, and wikis.',
    category: ['collaboration', 'product'],
    icon: <Database className="h-6 w-6" />,
    proficiency: 5
  },
  {
    id: 'miro',
    name: 'Miro',
    description: 'Online collaborative whiteboarding platform.',
    category: ['collaboration', 'product'],
    icon: <Users className="h-6 w-6" />,
    proficiency: 4
  },
  
  // Technical Skills
  {
    id: 'api',
    name: 'API Integration',
    description: 'Connecting and integrating services via APIs.',
    category: ['product'],
    icon: <Code className="h-6 w-6" />,
    proficiency: 4
  },
  {
    id: 'shopify',
    name: 'Shopify',
    description: 'E-commerce platform development and customization.',
    category: ['product'],
    icon: <Globe className="h-6 w-6" />,
    proficiency: 5
  },
  {
    id: 'sql',
    name: 'SQL',
    description: 'Database querying and data manipulation.',
    category: ['analytics'],
    icon: <Database className="h-6 w-6" />,
    proficiency: 3
  }
];

const categories = [
  { id: 'all', label: 'All Tools', icon: <Wrench className="h-5 w-5" />, color: 'from-indigo-600 to-purple-600' },
  { id: 'product', label: 'Product Management', icon: <BarChart4 className="h-5 w-5" />, color: 'from-blue-600 to-cyan-600' },
  { id: 'supply', label: 'Supply Chain', icon: <Truck className="h-5 w-5" />, color: 'from-emerald-600 to-green-600' },
  { id: 'analytics', label: 'Data & Analytics', icon: <PieChart className="h-5 w-5" />, color: 'from-amber-500 to-orange-600' },
  { id: 'collaboration', label: 'Collaboration', icon: <Users className="h-5 w-5" />, color: 'from-rose-600 to-pink-600' }
];

interface Methodology {
  name: string;
  description: string;
  icon: React.ReactNode;
  keyPoints: string[];
}

const methodologies: Methodology[] = [
  {
    name: 'Agile Project Management',
    description: 'Iterative approach to project management and software development that helps teams deliver value to their customers faster and with fewer headaches.',
    icon: <Workflow className="h-8 w-8" />,
    keyPoints: [
      'Scrum and Kanban framework implementation',
      'Sprint planning and retrospectives',
      'Continuous delivery and integration',
      'User story mapping and refinement',
      'Cross-functional team coordination'
    ]
  },
  {
    name: 'Lean Six Sigma',
    description: 'Data-driven methodology for eliminating defects and waste in business processes to improve operational efficiency and quality.',
    icon: <BarChart4 className="h-8 w-8" />,
    keyPoints: [
      'Process mapping and value stream analysis',
      'Root cause analysis techniques',
      'Statistical process control',
      'Waste elimination strategies',
      'Continuous improvement culture'
    ]
  },
  {
    name: 'Design Thinking',
    description: 'Human-centered approach to innovation that draws from the designer toolkit to integrate the needs of people, the possibilities of technology, and the requirements for business success.',
    icon: <Users className="h-8 w-8" />,
    keyPoints: [
      'Empathy mapping and user interviews',
      'Rapid prototyping and testing',
      'Ideation and brainstorming techniques',
      'User journey mapping',
      'Iterative design processes'
    ]
  }
];

export default function ToolsAndMethodologies() {
  const [activeCategory, setActiveCategory] = useState<ToolCategory>('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedTool, setSelectedTool] = useState<string | null>(null);
  const [activeMethodology, setActiveMethodology] = useState<string | null>(null);
  
  // Filter tools based on category and search query
  const filteredTools = toolsData
    .filter(tool => activeCategory === 'all' || tool.category.includes(activeCategory))
    .filter(tool => 
      searchQuery === '' || 
      tool.name.toLowerCase().includes(searchQuery.toLowerCase()) || 
      tool.description.toLowerCase().includes(searchQuery.toLowerCase())
    );
  
  // Get category color
  const getCategoryColor = (id: string) => {
    const category = categories.find(cat => cat.id === id);
    return category ? category.color : 'from-gray-600 to-gray-700';
  };
  
  return (
    <MotionWrapper className="space-y-8">
      <motion.div 
        className="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden"
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        viewport={{ once: true }}
      >
        <div className="p-6 sm:p-8">
          <div className="flex flex-col md:flex-row md:items-center justify-between mb-8 gap-4">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Tools & Technologies</h2>
            
            {/* Search Input */}
            <div className="relative max-w-xs">
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <Search className="w-4 h-4 text-gray-500 dark:text-gray-400" />
              </div>
              <input
                type="text"
                className="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-indigo-500 focus:border-indigo-500 block w-full pl-10 p-2.5 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-indigo-500 dark:focus:border-indigo-500"
                placeholder="Search tools..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>
          
          {/* Category Pills */}
          <div className="flex flex-wrap gap-2 mb-6">
            {categories.map((category) => {
              const isActive = activeCategory === category.id;
              return (
                <motion.button
                  key={category.id}
                  onClick={() => setActiveCategory(category.id as ToolCategory)}
                  className={`relative flex items-center px-4 py-2 rounded-full text-sm font-medium transition-all ${
                    isActive ? 'text-white shadow-md' : 'bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600'
                  }`}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  style={{
                    background: isActive ? `linear-gradient(to right, ${category.color.split(' ')[0].replace('from-', '')}, ${category.color.split(' ')[1].replace('to-', '')})` : ''
                  }}
                >
                  <span className={`mr-2 ${isActive ? 'text-white' : 'text-indigo-600 dark:text-indigo-400'}`}>
                    {category.icon}
                  </span>
                  {category.label}
                  {isActive && (
                    <motion.span
                      layoutId="activePill"
                      className="absolute inset-0 rounded-full z-0"
                      initial={false}
                      transition={{ type: 'spring', damping: 25, stiffness: 300 }}
                    />
                  )}
                </motion.button>
              );
            })}
          </div>
          
          {/* Tools Grid */}
          {filteredTools.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredTools.map((tool, index) => {
                const isSelected = selectedTool === tool.id;
                const categoryColor = getCategoryColor(tool.category[0]);
                
                return (
                  <motion.div
                    key={tool.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3, delay: index * 0.05 }}
                    className={`bg-gray-50 dark:bg-gray-700 rounded-xl overflow-hidden transition-all duration-300 ${
                      isSelected ? 'ring-2 ring-indigo-500 dark:ring-indigo-400 shadow-lg' : 'hover:shadow-md'
                    }`}
                    onClick={() => setSelectedTool(isSelected ? null : tool.id)}
                    whileHover={{ y: -4 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <div className="h-2 w-full" style={{ 
                      background: `linear-gradient(to right, ${categoryColor.split(' ')[0].replace('from-', '')}, ${categoryColor.split(' ')[1].replace('to-', '')})` 
                    }}></div>
                    <div className="p-5">
                      <div className="flex items-start">
                        <div className="bg-indigo-100 dark:bg-indigo-900/30 p-2 rounded-lg mr-4">
                          {tool.icon}
                        </div>
                        <div className="flex-1">
                          <div className="flex justify-between items-center">
                            <h3 className="font-semibold text-gray-900 dark:text-white">{tool.name}</h3>
                            <ChevronRight className={`w-5 h-5 text-gray-400 transition-transform ${isSelected ? 'rotate-90' : ''}`} />
                          </div>
                          <div className="flex mt-1">
                            {Array.from({ length: 5 }).map((_, i) => (
                              <Star
                                key={i}
                                className={`w-3.5 h-3.5 ${
                                  i < tool.proficiency 
                                    ? 'text-yellow-500 fill-yellow-500' 
                                    : 'text-gray-300 dark:text-gray-600'
                                }`}
                              />
                            ))}
                          </div>
                        </div>
                      </div>
                      
                      <AnimatePresence>
                        {isSelected && (
                          <motion.div
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: 'auto' }}
                            exit={{ opacity: 0, height: 0 }}
                            transition={{ duration: 0.3 }}
                            className="mt-4"
                          >
                            <p className="text-sm text-gray-600 dark:text-gray-300">{tool.description}</p>
                            
                            {/* Categories */}
                            <div className="mt-3 flex flex-wrap gap-1">
                              {tool.category.map(cat => (
                                <span
                                  key={`${tool.id}-${cat}`}
                                  className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-600 dark:text-gray-200"
                                >
                                  {cat === 'product' ? 'Product' : 
                                   cat === 'supply' ? 'Supply Chain' : 
                                   cat === 'analytics' ? 'Analytics' : 
                                   cat === 'collaboration' ? 'Collaboration' : cat}
                                </span>
                              ))}
                            </div>
                            
                            {/* Proficiency Description */}
                            <div className="mt-3 text-xs text-gray-500 dark:text-gray-400">
                              <span className="font-medium">
                                {tool.proficiency === 5 ? 'Expert Level: ' : 
                                 tool.proficiency === 4 ? 'Advanced Level: ' : 
                                 tool.proficiency === 3 ? 'Intermediate Level: ' : 
                                 tool.proficiency === 2 ? 'Basic Level: ' : 'Beginner Level: '}
                              </span>
                              {tool.proficiency === 5 ? 'Extensive experience and deep understanding' : 
                               tool.proficiency === 4 ? 'Strong capabilities with advanced features' : 
                               tool.proficiency === 3 ? 'Competent with core functionality' : 
                               tool.proficiency === 2 ? 'Familiar with basic operations' : 'Basic awareness and fundamentals'}
                            </div>
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          ) : (
            <div className="text-center py-12">
              <motion.div 
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="inline-block p-3 rounded-full bg-gray-100 dark:bg-gray-700 mb-4"
              >
                <Search className="h-6 w-6 text-gray-500 dark:text-gray-400" />
              </motion.div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">No tools found</h3>
              <p className="text-gray-500 dark:text-gray-400 mt-2">Try adjusting your search or filter criteria</p>
            </div>
          )}
        </div>
      </motion.div>
      
      {/* Methodologies Section */}
      <motion.div 
        className="bg-white dark:bg-gray-800 rounded-xl shadow-lg overflow-hidden"
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
        viewport={{ once: true }}
      >
        <div className="p-6 sm:p-8">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-8">Methodologies</h2>
          
          <div className="space-y-6">
            {methodologies.map((methodology, index) => {
              const isActive = activeMethodology === methodology.name;
              
              return (
                <motion.div 
                  key={methodology.name}
                  className={`bg-gray-50 dark:bg-gray-700/50 rounded-xl overflow-hidden transition-all duration-300 ${
                    isActive ? 'shadow-md' : ''
                  }`}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: index * 0.1 }}
                  viewport={{ once: true, amount: 0.1 }}
                >
                  <div 
                    className={`p-5 cursor-pointer flex items-center justify-between ${
                      isActive ? 'bg-gradient-to-r from-indigo-600 to-purple-600 text-white' : ''
                    }`}
                    onClick={() => setActiveMethodology(isActive ? null : methodology.name)}
                  >
                    <div className="flex items-center">
                      <div className={`p-2 rounded-lg mr-4 ${
                        isActive ? 'bg-white/20' : 'bg-indigo-100 dark:bg-indigo-900/30'
                      }`}>
                        {methodology.icon}
                      </div>
                      <h3 className={`font-semibold ${
                        isActive ? 'text-white' : 'text-gray-900 dark:text-white'
                      }`}>
                        {methodology.name}
                      </h3>
                    </div>
                    <ChevronRight className={`w-5 h-5 transition-transform ${
                      isActive ? 'rotate-90 text-white' : 'text-gray-400 dark:text-gray-500'
                    }`} />
                  </div>
                  
                  <AnimatePresence>
                    {isActive && (
                      <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: 'auto' }}
                        exit={{ opacity: 0, height: 0 }}
                        className="px-5 pb-5"
                      >
                        <p className="text-gray-600 dark:text-gray-300 mb-4 mt-3">
                          {methodology.description}
                        </p>
                        
                        <ul className="space-y-2">
                          {methodology.keyPoints.map((point, i) => (
                            <motion.li 
                              key={i}
                              className="flex items-start"
                              initial={{ opacity: 0, x: -10 }}
                              animate={{ opacity: 1, x: 0 }}
                              transition={{ delay: i * 0.1 }}
                            >
                              <Check className="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                              <span className="text-gray-600 dark:text-gray-300">{point}</span>
                            </motion.li>
                          ))}
                        </ul>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </motion.div>
              );
            })}
          </div>
        </div>
      </motion.div>
    </MotionWrapper>
  );
} 