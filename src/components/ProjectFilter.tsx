'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Search, Filter } from 'lucide-react';

interface ProjectItem {
  id: number;
  name: string;
  period: string;
  description: string;
  category: string;
  technologies: string[];
  image?: string;
}

const projects: ProjectItem[] = [
  {
    id: 1,
    name: 'Online Payment Gateway',
    period: 'January 2024 - April 2024',
    description: 'Led a team of 5 to implement and integrate an online payment gateway, enhancing the customer experience and streamlining the online payment process.',
    category: 'Web Development',
    technologies: ['API Integration', 'Payment Processing', 'Shopify'],
  },
  {
    id: 2,
    name: 'ERP Integration',
    period: 'March 2024 - June 2024',
    description: 'Spearheaded the addition of ERP features, including accounting, payment, and operations functionalities, ensuring seamless integration with third-party ERP systems.',
    category: 'Enterprise Systems',
    technologies: ['ERP', 'System Integration', 'Business Process Automation'],
  },
  {
    id: 3,
    name: 'Zawaya Backend System',
    period: 'January 2023 - Present',
    description: 'Developed the backend system for Zawaya from scratch to comply with specific business needs, enhancing operational efficiency and system reliability.',
    category: 'Backend Development',
    technologies: ['API Development', 'Database Design', 'System Architecture'],
  },
  {
    id: 4,
    name: 'Internal Flutter Delivery App',
    period: 'November 2024 - Present',
    description: 'Designed, coded, and tested a Flutter-based delivery app that provides real-time tracking of agents\' performance and availability, enhancing operational efficiency.',
    category: 'Mobile Development',
    technologies: ['Flutter', 'Dart', 'Real-time Tracking'],
  },
  {
    id: 5,
    name: 'Shopify Website Development',
    period: 'January 2025 - Present',
    description: 'Developed a new Shopify e-commerce website, improving user experience and driving an increase in customer orders.',
    category: 'E-commerce',
    technologies: ['Shopify', 'E-commerce', 'UI/UX Design'],
  },
  {
    id: 6,
    name: 'Internal Inventory Management Web App',
    period: 'March 2025 - Present',
    description: 'Designed, coded, and tested a TypeScript/React/Node web app for the warehouse team that helps track item movement, sales orders, and purchase orders, keeping the team performance always in check.',
    category: 'Web Development',
    technologies: ['React', 'TypeScript', 'Node.js', 'Inventory Management'],
  },
  {
    id: 7,
    name: 'Internal CRM Web App',
    period: 'March 2025 - Present',
    description: 'Designed, coded, and tested a TypeScript/React/Node web app for the sales team that tracks RFQs, QTNs, won and lost orders over the team, and keeps track of every action and its reactions to help understand customer and team behavior, leading to tracking performance and finance insights.',
    category: 'Web Development',
    technologies: ['React', 'TypeScript', 'Node.js', 'CRM'],
  }
];

const categories = ['All', 'Web Development', 'Mobile Development', 'E-commerce', 'Enterprise Systems', 'Backend Development'];

export default function ProjectFilter() {
  const [selectedCategory, setSelectedCategory] = useState('All');
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredProjects, setFilteredProjects] = useState<ProjectItem[]>(projects);

  useEffect(() => {
    const filtered = projects.filter((project) => {
      const matchesCategory = selectedCategory === 'All' || project.category === selectedCategory;
      const matchesSearch = 
        project.name.toLowerCase().includes(searchQuery.toLowerCase()) || 
        project.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        project.technologies.some(tech => tech.toLowerCase().includes(searchQuery.toLowerCase()));
      
      return matchesCategory && matchesSearch;
    });
    setFilteredProjects(filtered);
  }, [selectedCategory, searchQuery]);

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row gap-4 items-start md:items-center justify-between">
        <div className="relative flex-1">
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <Search className="w-5 h-5 text-gray-400" />
          </div>
          <input
            type="text"
            className="bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 border border-gray-300 dark:border-gray-700 rounded-md py-2 pl-10 pr-4 w-full focus:ring-indigo-500 focus:border-indigo-500"
            placeholder="Search projects..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        
        <div className="flex items-center gap-2 overflow-x-auto pb-2 md:pb-0 w-full md:w-auto">
          <Filter className="w-5 h-5 text-gray-500 dark:text-gray-400 flex-shrink-0" />
          {categories.map((category) => (
            <button
              key={category}
              onClick={() => setSelectedCategory(category)}
              className={`px-3 py-1 text-sm rounded-full whitespace-nowrap ${
                selectedCategory === category
                  ? 'bg-indigo-100 dark:bg-indigo-900 text-indigo-700 dark:text-indigo-300 border border-indigo-200 dark:border-indigo-800'
                  : 'bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 border border-gray-200 dark:border-gray-700 hover:bg-gray-200 dark:hover:bg-gray-700'
              }`}
            >
              {category}
            </button>
          ))}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <AnimatePresence>
          {filteredProjects.length > 0 ? (
            filteredProjects.map((project) => (
              <motion.div
                key={project.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 20 }}
                transition={{ duration: 0.3 }}
                className="bg-white dark:bg-gray-800 rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-shadow duration-300"
              >
                <div className="p-6">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">{project.name}</h3>
                  <p className="text-sm text-indigo-600 dark:text-indigo-400 mb-2">{project.period}</p>
                  <p className="text-gray-600 dark:text-gray-300 mb-4">{project.description}</p>
                  <div className="flex flex-wrap gap-2 mt-4">
                    {project.technologies.map((tech, index) => (
                      <span
                        key={index}
                        className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-300 text-xs rounded"
                      >
                        {tech}
                      </span>
                    ))}
                  </div>
                </div>
              </motion.div>
            ))
          ) : (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="col-span-full text-center py-10"
            >
              <p className="text-gray-500 dark:text-gray-400">No projects found matching your criteria.</p>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
} 