'use client';

import { useState, useEffect } from 'react';

interface BreakpointConfig {
  xs: number;
  sm: number;
  md: number;
  lg: number;
  xl: number;
  '2xl': number;
  '3xl': number;
}

const defaultBreakpoints: BreakpointConfig = {
  xs: 475,
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536,
  '3xl': 1920,
};

export interface ResponsiveState {
  width: number;
  height: number;
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  isLargeDesktop: boolean;
  breakpoint: keyof BreakpointConfig;
  orientation: 'portrait' | 'landscape';
  isTouch: boolean;
}

export const useResponsive = (breakpoints: Partial<BreakpointConfig> = {}): ResponsiveState => {
  const bp = { ...defaultBreakpoints, ...breakpoints };
  
  const [state, setState] = useState<ResponsiveState>({
    width: 0,
    height: 0,
    isMobile: false,
    isTablet: false,
    isDesktop: false,
    isLargeDesktop: false,
    breakpoint: 'xs',
    orientation: 'portrait',
    isTouch: false,
  });

  useEffect(() => {
    const updateState = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;
      const orientation = width > height ? 'landscape' : 'portrait';
      const isTouch = 'ontouchstart' in window || navigator.maxTouchPoints > 0;

      let breakpoint: keyof BreakpointConfig = 'xs';
      if (width >= bp['3xl']) breakpoint = '3xl';
      else if (width >= bp['2xl']) breakpoint = '2xl';
      else if (width >= bp.xl) breakpoint = 'xl';
      else if (width >= bp.lg) breakpoint = 'lg';
      else if (width >= bp.md) breakpoint = 'md';
      else if (width >= bp.sm) breakpoint = 'sm';
      else if (width >= bp.xs) breakpoint = 'xs';

      const isMobile = width < bp.md;
      const isTablet = width >= bp.md && width < bp.lg;
      const isDesktop = width >= bp.lg && width < bp['2xl'];
      const isLargeDesktop = width >= bp['2xl'];

      setState({
        width,
        height,
        isMobile,
        isTablet,
        isDesktop,
        isLargeDesktop,
        breakpoint,
        orientation,
        isTouch,
      });
    };

    // Initial update
    updateState();

    // Add event listener
    window.addEventListener('resize', updateState);
    window.addEventListener('orientationchange', updateState);

    // Cleanup
    return () => {
      window.removeEventListener('resize', updateState);
      window.removeEventListener('orientationchange', updateState);
    };
  }, [bp]);

  return state;
};

// Utility hook for specific breakpoint checks
export const useBreakpoint = (breakpoint: keyof BreakpointConfig): boolean => {
  const { breakpoint: current } = useResponsive();
  const breakpointOrder: (keyof BreakpointConfig)[] = ['xs', 'sm', 'md', 'lg', 'xl', '2xl', '3xl'];
  
  const currentIndex = breakpointOrder.indexOf(current);
  const targetIndex = breakpointOrder.indexOf(breakpoint);
  
  return currentIndex >= targetIndex;
};

// Utility hook for mobile detection
export const useIsMobile = (): boolean => {
  const { isMobile } = useResponsive();
  return isMobile;
};

// Utility hook for touch device detection
export const useIsTouch = (): boolean => {
  const { isTouch } = useResponsive();
  return isTouch;
};
