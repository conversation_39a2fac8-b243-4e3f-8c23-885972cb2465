'use client';

import { useEffect } from 'react';
import { motion } from 'framer-motion';
import Header from '@/components/Header';
import HeroSection from '@/components/HeroSection';
import AboutMe from '@/components/AboutMe';
import Portfolio from '@/components/Portfolio';
import ContactForm from '@/components/ContactForm';
import Footer from '@/components/Footer';
import { useScrollReveal, useStaggerAnimation } from '@/components/animations/GSAPAnimations';

// Section divider component with cyber design
const SectionDivider = ({ className = "" }: { className?: string }) => (
  <div className={`relative h-24 ${className}`}>
    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-primary/20 to-transparent" />
    <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
      <div className="w-2 h-2 bg-primary rounded-full animate-pulse" />
    </div>
  </div>
);

// Section header component with cyber styling
const SectionHeader = ({ title, description }: { title: string; description?: string }) => (
  <div className="text-center mb-16">
    <h2 className="section-title">
      {title}
    </h2>
    {description && (
      <motion.p
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.2 }}
        viewport={{ once: true }}
        className="mt-6 text-muted-foreground max-w-3xl mx-auto text-lg font-mono"
      >
        {description}
      </motion.p>
    )}
    <motion.div
      className="flex justify-center mt-6"
      initial={{ width: 0 }}
      whileInView={{ width: "auto" }}
      viewport={{ once: true }}
    >
      <motion.div
        className="h-1 w-24 bg-primary rounded-full"
        initial={{ width: 0 }}
        whileInView={{ width: 120 }}
        transition={{ duration: 0.8, delay: 0.3 }}
        viewport={{ once: true }}
      ></motion.div>
    </motion.div>
  </div>
);

export default function Home() {
  // GSAP animations
  useScrollReveal('.enhanced-section', {
    from: { opacity: 0, y: 100 },
    to: { opacity: 1, y: 0, duration: 1.2 }
  });

  // Use useStaggerAnimation for cards instead
  useStaggerAnimation('.cyber-card', {
    from: { opacity: 0, scale: 0.8, y: 50 },
    to: { opacity: 1, scale: 1, y: 0, duration: 1 }
  }, 0.2);
  
  return (
    <div className="min-h-screen bg-background text-foreground">
      <Header />

      <main>
        {/* Hero Section */}
        <HeroSection />

        <SectionDivider />

        {/* About Section */}
        <section id="about" className="section-container bg-card relative enhanced-section">
          <motion.div
            className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-primary via-accent to-primary"
            initial={{ width: 0 }}
            whileInView={{ width: '100%' }}
            transition={{ duration: 1.5 }}
            viewport={{ once: true }}
          />
          <div className="relative z-10">
            <SectionHeader
              title="ABOUT_ME.EXE"
              description="Product Manager & Supply Chain Specialist with a passion for creating efficient solutions."
            />
            <AboutMe />
          </div>
        </section>

        <SectionDivider className="rotate-180" />

        {/* Projects Section */}
        <section id="projects" className="section-container bg-background relative enhanced-section">
          <SectionHeader
            title="PROJECTS.DIR"
            description="Showcasing innovative solutions and measurable results."
          />
          <Portfolio />
        </section>

        <SectionDivider />

        {/* Contact Section */}
        <section id="contact" className="section-container bg-card relative enhanced-section">
          <SectionHeader
            title="CONTACT.SH"
            description="Ready to collaborate? Let's build something amazing together."
          />
          <div className="container-tight">
            <ContactForm />
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
}