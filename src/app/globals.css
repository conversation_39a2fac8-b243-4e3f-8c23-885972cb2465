@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600;700&family=Space+Grotesk:wght@300;400;500;600;700&display=swap');

@layer base {
  :root {
    /* Modern Professional Color Palette */
    --background: 220 27% 8%;
    --foreground: 0 0% 98%;
    --card: 220 27% 12%;
    --card-foreground: 0 0% 95%;
    --popover: 220 27% 10%;
    --popover-foreground: 0 0% 98%;

    /* Sophisticated Primary Colors */
    --primary: 180 100% 70%;         /* Bright <PERSON>an */
    --primary-foreground: 220 27% 8%;
    --secondary: 280 100% 70%;       /* Vibrant Purple */
    --secondary-foreground: 0 0% 98%;
    --accent: 140 100% 60%;          /* Modern Green */
    --accent-foreground: 220 27% 8%;
    --warning: 35 100% 65%;          /* Warm Orange */
    --warning-foreground: 220 27% 8%;

    /* Additional Colors */
    --info: 200 100% 70%;            /* Sky Blue */
    --info-foreground: 220 27% 8%;
    --success: 140 100% 60%;         /* Success Green */
    --success-foreground: 220 27% 8%;

    /* Neutral Colors */
    --muted: 220 27% 18%;
    --muted-foreground: 0 0% 65%;
    --border: 220 27% 20%;
    --input: 220 27% 15%;
    --ring: 180 100% 70%;
    --destructive: 0 85% 60%;
    --destructive-foreground: 0 0% 98%;

    /* Design System Variables */
    --radius: 0.5rem;
    --font-mono: 'JetBrains Mono', monospace;
    --font-display: 'Space Grotesk', sans-serif;

    /* Enhanced Glow Effects */
    --glow-cyan: 0 0 25px rgba(0, 255, 255, 0.4);
    --glow-purple: 0 0 25px rgba(168, 85, 247, 0.4);
    --glow-green: 0 0 25px rgba(34, 197, 94, 0.4);
    --glow-orange: 0 0 25px rgba(251, 146, 60, 0.4);
    --glow-blue: 0 0 25px rgba(59, 130, 246, 0.4);

    /* Beautiful Gradients */
    --gradient-cyber: linear-gradient(135deg, hsl(180 100% 70%) 0%, hsl(280 100% 70%) 30%, hsl(140 100% 60%) 70%, hsl(35 100% 65%) 100%);
    --gradient-primary: linear-gradient(135deg, hsl(180 100% 70%) 0%, hsl(200 100% 70%) 100%);
    --gradient-secondary: linear-gradient(135deg, hsl(280 100% 70%) 0%, hsl(260 100% 75%) 100%);
    --gradient-accent: linear-gradient(135deg, hsl(140 100% 60%) 0%, hsl(120 100% 65%) 100%);
    --gradient-dark: linear-gradient(135deg, hsl(220 27% 8%) 0%, hsl(220 27% 12%) 100%);
    --gradient-glow: radial-gradient(circle at center, rgba(0, 255, 255, 0.08) 0%, transparent 70%);
  }

  .light {
    --background: 0 0% 98%;
    --foreground: 220 27% 8%;
    --card: 0 0% 100%;
    --card-foreground: 220 27% 8%;
    --popover: 0 0% 100%;
    --popover-foreground: 220 27% 8%;

    --primary: 180 100% 45%;
    --primary-foreground: 0 0% 98%;
    --secondary: 280 100% 55%;
    --secondary-foreground: 0 0% 98%;
    --accent: 140 100% 45%;
    --accent-foreground: 0 0% 98%;
    --warning: 35 100% 50%;
    --warning-foreground: 0 0% 98%;
    --info: 200 100% 50%;
    --info-foreground: 0 0% 98%;
    --success: 140 100% 45%;
    --success-foreground: 0 0% 98%;

    --muted: 220 27% 95%;
    --muted-foreground: 220 27% 45%;
    --border: 220 27% 90%;
    --input: 220 27% 95%;

    --glow-cyan: 0 0 20px rgba(0, 255, 255, 0.2);
    --glow-purple: 0 0 20px rgba(168, 85, 247, 0.2);
    --glow-green: 0 0 20px rgba(34, 197, 94, 0.2);
    --glow-orange: 0 0 20px rgba(251, 146, 60, 0.2);
    --glow-blue: 0 0 20px rgba(59, 130, 246, 0.2);
  }
}

@layer base {
  * {
    @apply border-solid border-0;
  }
  
  body {
    @apply bg-background text-foreground font-sans antialiased;
  }
  
  h1, h2, h3, h4, h5, h6 {
    @apply font-bold tracking-tight;
  }
  
  /* Smooth scrolling */
  html {
    scroll-behavior: smooth;
  }
  
  /* Hide scrollbar for Chrome, Safari and Opera */
  ::-webkit-scrollbar {
    display: none;
  }
  
  /* Hide scrollbar for IE, Edge and Firefox */
  html {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }
}

@layer components {
  /* Responsive Container System */
  .section-container {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 sm:py-16 md:py-20 lg:py-24;
  }

  .container-tight {
    @apply max-w-4xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .container-wide {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 xl:px-12;
  }

  /* Responsive Typography */
  .section-title {
    @apply text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold mb-6 sm:mb-8;
    font-family: 'Space Grotesk', sans-serif;
    background: var(--gradient-cyber);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0 0 30px rgba(0, 255, 255, 0.3);
  }

  .section-subtitle {
    @apply text-lg sm:text-xl md:text-2xl text-muted-foreground max-w-3xl mx-auto font-mono;
  }

  .hero-title {
    @apply text-4xl sm:text-5xl md:text-6xl lg:text-7xl xl:text-8xl font-bold font-display;
  }

  .hero-subtitle {
    @apply text-base sm:text-lg md:text-xl lg:text-2xl text-muted-foreground max-w-3xl mx-auto font-mono;
  }

  .card {
    @apply bg-card text-card-foreground rounded-2xl overflow-hidden transition-all duration-500 border border-border/50;
    box-shadow: var(--shadow-secondary);
  }

  .card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-primary);
  }

  .btn {
    @apply inline-flex items-center justify-center rounded-full font-medium transition-all duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none;
  }

  .btn-primary {
    @apply text-primary-foreground px-8 py-3 rounded-full relative overflow-hidden;
    background: var(--gradient-primary);
    box-shadow: var(--shadow-primary);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .btn-primary:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: var(--shadow-glow);
  }

  .btn-secondary {
    @apply bg-secondary text-secondary-foreground hover:bg-secondary/80 transition-all duration-300 px-8 py-3 rounded-full;
    box-shadow: var(--shadow-secondary);
  }

  .btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-primary);
  }

  .form-input {
    @apply block w-full rounded-xl border-input bg-background shadow-sm focus:border-primary focus:ring-primary transition-all duration-300;
  }

  /* Enhanced glass effect */
  .glass {
    @apply bg-white/10 dark:bg-foreground/10 backdrop-blur-xl border border-white/20 dark:border-gray-800/20 rounded-2xl;
    backdrop-filter: blur(20px);
  }

  /* Responsive Grid System */
  .grid-responsive {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6;
  }

  .grid-responsive-2 {
    @apply grid grid-cols-1 md:grid-cols-2 gap-6 lg:gap-8;
  }

  .grid-responsive-4 {
    @apply grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 sm:gap-6;
  }

  /* Responsive Flex Utilities */
  .flex-responsive {
    @apply flex flex-col sm:flex-row gap-4 sm:gap-6;
  }

  .flex-responsive-center {
    @apply flex flex-col sm:flex-row gap-4 sm:gap-6 items-center justify-center;
  }

  /* Modern cards with enhanced hover effect */
  .hover-card {
    @apply relative overflow-hidden rounded-2xl transition-all duration-500 bg-card border border-border/50;
    box-shadow: var(--shadow-secondary);
  }

  .hover-card:hover {
    transform: translateY(-12px) rotateX(5deg) rotateY(5deg);
    box-shadow: var(--shadow-glow);
  }

  /* Responsive Spacing */
  .space-responsive {
    @apply space-y-8 sm:space-y-12 lg:space-y-16;
  }

  .gap-responsive {
    @apply gap-4 sm:gap-6 lg:gap-8;
  }

  /* Mobile-First Button Styles */
  .btn-responsive {
    @apply px-4 py-2 sm:px-6 sm:py-3 text-sm sm:text-base;
  }

  /* Responsive Text Alignment */
  .text-responsive-center {
    @apply text-center sm:text-left;
  }

  .text-responsive-left {
    @apply text-left sm:text-center lg:text-left;
  }

  /* Mobile-First Scrollbar Styles */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Touch-friendly interactions */
  .touch-target {
    @apply min-h-[44px] min-w-[44px];
  }

  /* Responsive visibility utilities */
  .mobile-only {
    @apply block sm:hidden;
  }

  .desktop-only {
    @apply hidden sm:block;
  }

  .tablet-up {
    @apply hidden md:block;
  }

  /* Responsive padding and margins */
  .p-responsive {
    @apply p-4 sm:p-6 lg:p-8;
  }

  .px-responsive {
    @apply px-4 sm:px-6 lg:px-8;
  }

  .py-responsive {
    @apply py-8 sm:py-12 lg:py-16;
  }

  /* Responsive text sizes */
  .text-responsive-sm {
    @apply text-sm sm:text-base;
  }

  .text-responsive-base {
    @apply text-base sm:text-lg;
  }

  .text-responsive-lg {
    @apply text-lg sm:text-xl lg:text-2xl;
  }

  /* Device-specific optimizations */
  @media (max-width: 640px) {
    .cyber-card {
      @apply rounded-lg;
    }

    .terminal-window {
      @apply rounded-lg;
    }

    .section-title {
      line-height: 1.1;
    }
  }

  @media (max-width: 475px) {
    .hero-title {
      line-height: 0.9;
    }

    .cyber-btn, .cyber-btn-outline {
      @apply px-4 py-2 text-sm;
    }
  }

  /* Landscape phone optimizations */
  @media (max-height: 500px) and (orientation: landscape) {
    .section-container {
      @apply py-8;
    }

    .hero-title {
      @apply text-3xl;
    }
  }

  /* Tablet optimizations */
  @media (min-width: 768px) and (max-width: 1024px) {
    .grid-responsive {
      @apply grid-cols-2;
    }
  }

  /* High DPI displays */
  @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .cyber-card {
      border-width: 0.5px;
    }
  }

  /* Enhanced gradient button */
  .gradient-btn {
    @apply relative overflow-hidden rounded-full px-8 py-3 font-medium text-white transition-all duration-500;
    background: var(--gradient-primary);
    background-size: 200% auto;
  }

  .gradient-btn:hover {
    background-position: right center;
    transform: translateY(-3px) scale(1.05);
    box-shadow: var(--shadow-glow);
  }

  /* Horizontal scroll container */
  .horizontal-scroll {
    @apply flex gap-6 overflow-x-auto pb-4 snap-x snap-mandatory;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .horizontal-scroll::-webkit-scrollbar {
    display: none;
  }

  .horizontal-scroll-item {
    @apply flex-shrink-0 snap-center;
  }

  /* Modern scroll indicators */
  .scroll-indicator {
    @apply absolute top-1/2 transform -translate-y-1/2 z-10 p-3 rounded-full transition-all duration-300;
    background: var(--gradient-primary);
    box-shadow: var(--shadow-primary);
  }

  .scroll-indicator:hover {
    transform: translateY(-50%) scale(1.1);
    box-shadow: var(--shadow-glow);
  }

  .scroll-indicator-left {
    @apply left-4;
  }

  .scroll-indicator-right {
    @apply right-4;
  }

  /* Scrollbar hide utility */
  .scrollbar-hide {
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Cyber-Brutalist Components */
  .cyber-grid {
    background-image:
      linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),
      linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
  }

  .cyber-text {
    @apply font-mono text-primary;
    text-shadow: 0 0 10px currentColor;
  }

  .glitch-text {
    position: relative;
    color: hsl(var(--foreground));
    font-weight: 700;
  }

  .glitch-text::before,
  .glitch-text::after {
    content: attr(data-text);
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }

  .glitch-text::before {
    animation: glitch-1 0.5s infinite;
    color: hsl(var(--primary));
    z-index: -1;
  }

  .glitch-text::after {
    animation: glitch-2 0.5s infinite;
    color: hsl(var(--accent));
    z-index: -2;
  }

  .cyber-card {
    @apply bg-card border border-border rounded-lg overflow-hidden relative;
    background: linear-gradient(135deg, hsl(var(--card)) 0%, hsl(var(--muted)) 100%);
    box-shadow:
      0 4px 20px rgba(0, 0, 0, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .cyber-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, hsl(var(--primary)), transparent);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .cyber-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow:
      0 20px 40px rgba(0, 0, 0, 0.4),
      0 0 20px hsl(var(--primary) / 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }

  .cyber-card:hover::before {
    opacity: 1;
  }

  .cyber-btn {
    @apply relative inline-flex items-center justify-center px-6 py-3 font-mono font-semibold text-sm uppercase tracking-wider;
    background: var(--gradient-primary);
    border: 1px solid hsl(var(--primary));
    color: hsl(var(--primary-foreground));
    clip-path: polygon(0 0, calc(100% - 10px) 0, 100% 100%, 10px 100%);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    text-shadow: 0 0 10px currentColor;
    box-shadow:
      0 4px 15px rgba(0, 0, 0, 0.3),
      var(--glow-cyan),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
  }

  .cyber-btn::before {
    content: '';
    position: absolute;
    inset: 1px;
    background: transparent;
    clip-path: polygon(0 0, calc(100% - 10px) 0, 100% 100%, 10px 100%);
    transition: all 0.3s ease;
    z-index: -1;
  }

  .cyber-btn:hover {
    transform: translateY(-2px);
    box-shadow:
      0 8px 25px rgba(0, 0, 0, 0.4),
      var(--glow-cyan),
      var(--glow-blue);
  }

  .cyber-btn:hover::before {
    background: transparent;
  }

  .cyber-btn-outline {
    @apply cyber-btn;
    background: transparent;
    color: hsl(var(--primary));
  }

  .cyber-btn-outline::before {
    background: transparent;
  }

  .cyber-btn-outline:hover::before {
    background: var(--gradient-primary);
    opacity: 0.2;
  }

  .cyber-btn-outline:hover {
    color: hsl(var(--primary-foreground));
  }

  .neon-border {
    border: 2px solid hsl(var(--primary));
    box-shadow:
      0 0 10px hsl(var(--primary) / 0.5),
      inset 0 0 10px hsl(var(--primary) / 0.1);
  }

  .terminal-window {
    @apply bg-card border border-border rounded-lg overflow-hidden;
    background: linear-gradient(135deg, hsl(var(--card)) 0%, hsl(var(--muted)) 100%);
    box-shadow:
      0 10px 30px rgba(0, 0, 0, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }

  .terminal-header {
    @apply flex items-center gap-2 px-4 py-3 bg-muted border-b border-border;
  }

  .terminal-dot {
    @apply w-3 h-3 rounded-full;
  }

  .terminal-dot.red {
    background: #ff5f56;
  }

  .terminal-dot.yellow {
    background: #ffbd2e;
  }

  .terminal-dot.green {
    background: #27ca3f;
  }
}

/* Enhanced Animations */
@keyframes glitch-1 {
  0%, 14%, 15%, 49%, 50%, 99%, 100% {
    transform: translate(0);
  }
  15%, 49% {
    transform: translate(-2px, 2px);
  }
}

@keyframes glitch-2 {
  0%, 20%, 21%, 62%, 63%, 99%, 100% {
    transform: translate(0);
  }
  21%, 62% {
    transform: translate(2px, -2px);
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes slideDown {
  from { transform: translateY(-20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes slideLeft {
  from { transform: translateX(20px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes slideRight {
  from { transform: translateX(-20px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
  100% { transform: translateY(0px); }
}

@keyframes gradientBg {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes glow {
  0% { box-shadow: 0 0 5px rgba(139, 92, 246, 0.3); }
  50% { box-shadow: 0 0 20px rgba(139, 92, 246, 0.6), 0 0 30px rgba(139, 92, 246, 0.4); }
  100% { box-shadow: 0 0 5px rgba(139, 92, 246, 0.3); }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% { transform: translate3d(0, 0, 0); }
  40%, 43% { transform: translate3d(0, -8px, 0); }
  70% { transform: translate3d(0, -4px, 0); }
  90% { transform: translate3d(0, -2px, 0); }
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@keyframes scaleIn {
  from { transform: scale(0); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}

@keyframes slideInFromLeft {
  from { transform: translateX(-100%); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes slideInFromRight {
  from { transform: translateX(100%); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes morphing {
  0% { border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%; }
  50% { border-radius: 30% 60% 70% 40% / 50% 60% 30% 60%; }
  100% { border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%; }
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out forwards;
}

.animate-slide-up {
  animation: slideUp 0.5s ease-in-out forwards;
}

.animate-slide-down {
  animation: slideDown 0.5s ease-in-out forwards;
}

.animate-slide-left {
  animation: slideLeft 0.5s ease-in-out forwards;
}

.animate-slide-right {
  animation: slideRight 0.5s ease-in-out forwards;
}

.animate-pulse {
  animation: pulse 2s ease-in-out infinite;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-gradient-bg {
  animation: gradientBg 15s ease infinite;
  background-size: 400% 400%;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

.animate-bounce {
  animation: bounce 1s ease-in-out infinite;
}

.animate-rotate {
  animation: rotate 1s linear infinite;
}

.animate-scale-in {
  animation: scaleIn 0.5s ease-out forwards;
}

.animate-slide-in-left {
  animation: slideInFromLeft 0.6s ease-out forwards;
}

.animate-slide-in-right {
  animation: slideInFromRight 0.6s ease-out forwards;
}

.animate-morphing {
  animation: morphing 8s ease-in-out infinite;
}

.delay-75 {
  animation-delay: 75ms;
}

.delay-100 {
  animation-delay: 100ms;
}

.delay-150 {
  animation-delay: 150ms;
}

.delay-200 {
  animation-delay: 200ms;
}

.delay-300 {
  animation-delay: 300ms;
}

.delay-400 {
  animation-delay: 400ms;
}

.delay-500 {
  animation-delay: 500ms;
}

/* Text shimmer effect */
.text-shimmer {
  background: linear-gradient(to right, hsl(var(--primary)) 20%, hsl(var(--accent)) 40%, hsl(var(--accent)) 60%, hsl(var(--primary)) 80%);
  background-size: 200% auto;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: gradientBg 8s linear infinite;
}

/* Gradient border */
.gradient-border {
  position: relative;
  border-radius: var(--radius);
  overflow: hidden;
}

.gradient-border::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(
    45deg,
    hsl(var(--primary)),
    hsl(var(--accent)),
    hsl(var(--primary)),
    hsl(var(--accent))
  );
  background-size: 400% 400%;
  animation: gradientBg 15s ease infinite;
  z-index: -1;
  border-radius: calc(var(--radius) + 2px);
}

/* Card glow effect */
.card-glow {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.card-glow:hover {
  box-shadow: 0 0 25px 5px rgba(var(--primary), 0.3);
  transform: translateY(-5px);
}

/* Link underline animation */
.link-underline {
  position: relative;
}

.link-underline::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: -2px;
  left: 0;
  background: linear-gradient(
    to right,
    hsl(var(--primary)),
    hsl(var(--accent))
  );
  transition: width 0.3s ease;
}

.link-underline:hover::after {
  width: 100%;
} 