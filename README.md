# Ma<PERSON><PERSON>

This is a modern, interactive personal portfolio website built with Next.js, TypeScript, TailwindCSS, and enhanced with GSAP animations.

## ✨ Enhanced Features

- **Modern UI Design**: Updated color scheme with vibrant gradients and enhanced shadows
- **Horizontal Scrolling**: Smooth horizontal navigation for projects and skills sections
- **GSAP Animations**: Advanced timeline animations, scroll-triggered effects, and parallax elements
- **Micro-interactions**: Enhanced hover effects, ripple buttons, and magnetic interactions
- **Responsive Design**: Optimized for all device sizes with touch/swipe support
- **Interactive Components**: Animated inputs, floating action buttons, and tilt cards
- **Enhanced Typography**: Gradient text effects and improved font hierarchy
- **Glass Morphism**: Modern glass effects with backdrop blur
- **3D Effects**: Hover animations with perspective and rotation
- **Smooth Transitions**: Page and section transitions with stagger animations

## 🚀 Key Components

### Enhanced Horizontal Scrolling
- **HorizontalProjectsGrid**: Interactive project showcase with smooth scrolling and modal views
- **HorizontalSkillsGrid**: Skill categories with horizontal navigation and animated progress bars

### GSAP Animation System
- **GSAPAnimations**: Comprehensive animation utilities including scroll-triggered reveals, parallax effects, and magnetic interactions
- **Timeline Animations**: Staggered animations for section transitions
- **SVG Path Animations**: Animated icons and decorative elements

### Micro-interactions
- **RippleButton**: Click effects with customizable variants
- **AnimatedInput**: Focus effects with floating labels
- **TiltCard**: 3D hover effects with magnetic attraction
- **FloatingActionButton**: Positioned FABs with tooltips

### Enhanced UI Elements
- **Modern Color Scheme**: Vibrant blue-purple gradients with enhanced shadows
- **Glass Effects**: Backdrop blur with border styling
- **Enhanced Cards**: 3D hover effects and smooth transitions
- **Gradient Text**: Section titles with gradient effects

## Getting Started

### Prerequisites

- Node.js (v18.0.0 or later)
- npm (v8.0.0 or later)

### Installation

1. Clone the repository
```bash
git clone <repository-url>
cd portfolio
```

2. Install dependencies
```bash
npm install
```

3. Start the development server
```bash
npm run dev
```

4. Open your browser and navigate to `http://localhost:3000`

## Building for Production

To create a production build:

```bash
npm run build
```

To start the production server:

```bash
npm start
```

## Technologies Used

- Next.js
- TypeScript
- React
- TailwindCSS
- Lucide React (for icons)

## License

This project is open source and available under the MIT License. 